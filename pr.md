## Summary
This PR introduces a comprehensive grid-search optimizable parameter system for the mulberri decision tree implementation, replacing fixed stopping criteria with adaptive percentage-based thresholds and implementing fractional max depth calculation for better generalization across different dataset sizes.

## Problems Solved

### 1. Dataset Scaling Problem
**Issue**: Fixed stopping criteria don't scale across dataset sizes
- Small datasets: Risk of single-node trees (too restrictive)
- Large datasets: Risk of overfitting (too permissive)
- **Solution**: Hybrid adaptive thresholds using percentage-based parameters with absolute minimums

### 2. Overfitting Prevention
**Before**: Trees could grow too deep without proper regularization
**After**: Conservative depth limits with fractional calculation based on feature count

### 3. Grid-Search Optimization Support
**Issue**: Hard-coded parameters prevented hyperparameter optimization
**Solution**: Percentage-based parameters that scale with dataset size

## Key Features

### Grid-Search Optimizable Parameters
Implements percentage-based thresholds with absolute minimums for automatic scaling:

```go
// Adaptive threshold calculation with absolute minimums
func (tb *TreeBuilder) calculateAdaptiveThresholds(datasetSize int) {
    percentageSplit := int(math.Ceil(float64(datasetSize) * tb.config.MinSamplesSplitPercent))
    percentageLeaf := int(math.Ceil(float64(datasetSize) * tb.config.MinSamplesLeafPercent))

    // Hybrid approach: use larger of percentage-based or absolute minimum
    tb.adaptiveMinSamplesSplit = max(percentageSplit, 2) // Minimum 2 for splitting
    tb.adaptiveMinSamplesLeaf = max(percentageLeaf, 1)   // Minimum 1 for leaf
}
```

**New Default Parameters**:
```go
// Grid-search optimizable parameters
DefaultMinSamplesSplitPercent = 0.01   // 1% of dataset
DefaultMinSamplesLeafPercent = 0.005   // 0.5% of dataset
DefaultMinSplitImprovement = 0.01      // 1% gain ratio improvement
DefaultMaxDepthAlpha = 0.8             // Conservative depth: 0.8 * log2(features)
```

### Fractional Max Depth Calculation
Implements conservative depth limits based on feature count:

```go
// Calculates effective max depth using fractional multiplier
func (tb *TreeBuilder) calculateEffectiveMaxDepth(numFeatures int) int {
    if tb.config.MaxDepthExplicit > 0 {
        return tb.config.MaxDepthExplicit // Use explicit value if provided
    }

    // Calculate fractional depth: alpha * log2(features)
    fractionalDepth := tb.config.MaxDepthAlpha * math.Log2(float64(numFeatures))
    return int(math.Round(fractionalDepth))
}
```

### CLI Integration
New command-line flags for grid-search optimization:

```bash
# Grid-search optimizable training
mulberri train -i data.csv -t target -o model.dt -f features.yaml \
  --min-samples-split-percent 0.02 --min-samples-leaf-percent 0.01 \
  --min-split-improvement 0.005 --max-depth-alpha 1.2

# Explicit max depth instead of fractional
mulberri train -i data.csv -t target -o model.dt -f features.yaml --max-depth 8
```

## Results

### Dataset Performance with New Parameters
| Dataset | Size | Tree Structure | Adaptive Thresholds | Max Depth |
|---------|------|----------------|-------------------|-----------|
| **Tennis** | 14 samples | 8 nodes, depth 2 | split=2 (14.3%), leaf=1 (7.1%) | 2 (0.8 × log₂(4)) |
| **Iris** | 150 samples | 5 nodes, depth 2 | split=2 (1.3%), leaf=1 (0.7%) | 2 (0.8 × log₂(4)) |

### Adaptive Threshold Behavior
- **Small datasets (≤50)**: Protected by absolute minimums (split≥2, leaf≥1)
- **Medium datasets (50-1000)**: Percentage-based scaling takes effect
- **Large datasets (1000+)**: Full percentage scaling with proper regularization

### Grid-Search Benefits
- **Percentage-based parameters**: Scale automatically with dataset size
- **Fractional max depth**: Conservative depth limits prevent overfitting
- **Minimum improvement threshold**: Ensures meaningful splits (1% gain ratio)
- **Hyperparameter optimization ready**: All parameters tunable for grid-search

## Files Modified

**Core Algorithm**:
- `internal/training/builder.go` - Adaptive thresholds and fractional max depth calculation
- `internal/training/stopping.go` - Updated stopping criteria with adaptive parameters
- `internal/config/defaults.go` - New grid-search optimizable default parameters

**CLI Integration**:
- `internal/io/cli/train_command.go` - New grid-search parameter flags
- `internal/io/cli/config_types.go` - Enhanced configuration validation

**Testing**:
- `internal/training/builder_test.go` - Updated tests for new parameter system
- `internal/io/cli/config_types_test.go` - Enhanced validation tests

## Verification

```bash
# Test with default grid-search parameters
./mulberri train -i examples/tennis.csv -f examples/tennis.yaml -o tennis.dt -t Play --verbose

# Test with custom grid-search parameters
./mulberri train -i examples/iris.csv -f examples/iris.yaml -o iris.dt -t species \
  --min-samples-split-percent 0.02 --min-samples-leaf-percent 0.01 \
  --min-split-improvement 0.005 --max-depth-alpha 1.0 --verbose
```

**Expected Results with New Parameters**:
- Tennis (14 samples): 8 nodes at depth 2, adaptive thresholds: split=2, leaf=1
- Iris (150 samples): 5 nodes at depth 2, adaptive thresholds: split=2, leaf=1

## Breaking Changes
- Replaced fixed `MinSamplesSplit`/`MinSamplesLeaf` with percentage-based system
- Replaced fixed `MaxDepth` with fractional `MaxDepthAlpha` calculation
- Added `MinSplitImprovement` requirement for all splits
- New CLI flags for grid-search optimization

## Migration Guide
- **Old**: `--max-depth 10` → **New**: `--max-depth-alpha 1.5` or `--max-depth 10`
- **Old**: Fixed thresholds → **New**: Percentage-based with automatic scaling
- **New parameters**: All percentage values should be between 0.0 and 1.0

## Technical Details

### Adaptive Threshold Algorithm
```go
// For dataset size N with percentages P_split and P_leaf:
adaptiveMinSamplesSplit = max(ceil(N * P_split), 2)
adaptiveMinSamplesLeaf = max(ceil(N * P_leaf), 1)

// Example: 150 samples with 1% split, 0.5% leaf
// split = max(ceil(150 * 0.01), 2) = max(2, 2) = 2
// leaf = max(ceil(150 * 0.005), 1) = max(1, 1) = 1
```

### Fractional Max Depth
```go
// Conservative depth calculation prevents overfitting
effectiveMaxDepth = round(alpha * log2(numFeatures))

// Example: 4 features with alpha=0.8
// depth = round(0.8 * log2(4)) = round(0.8 * 2) = round(1.6) = 2
```
