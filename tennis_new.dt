{"features": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}, {"name": "Outlook", "type": "string"}, {"name": "Temperature", "type": "string"}, {"name": "Wind", "type": "string"}], "classes": ["No", "Yes"], "metadata": {"created_at": "2025-10-02T14:55:03.036447752+03:00", "algorithm": "C4.5", "max_depth": 2, "min_samples": 2, "criterion": "entropy", "total_nodes": 8, "leaf_nodes": 5, "training_samples": 14, "target_column": "Play"}, "root": {"type": "decision", "feature": {"name": "Outlook", "type": "string"}, "split_value": "Outlook", "children": {"Overcast": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 4}, "samples": 4, "confidence": 1}, "Rain": {"type": "decision", "feature": {"name": "Wind", "type": "string"}, "split_value": "Wind", "children": {"Strong": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 2}, "samples": 2, "confidence": 1}, "Weak": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 3}, "samples": 3, "confidence": 1}}, "class_distribution": {"No": 2, "Yes": 3}, "samples": 5, "confidence": 0.6}, "Sunny": {"type": "decision", "feature": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}, "split_value": "<PERSON><PERSON><PERSON><PERSON>", "children": {"High": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 3}, "samples": 3, "confidence": 1}, "Normal": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 2}, "samples": 2, "confidence": 1}}, "class_distribution": {"No": 3, "Yes": 2}, "samples": 5, "confidence": 0.6}}, "class_distribution": {"No": 5, "Yes": 9}, "samples": 14, "confidence": 0.6428571428571429}}