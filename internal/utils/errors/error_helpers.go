// Package errors provides centralized error handling utilities to eliminate code duplication.
//
// This package consolidates common error patterns found throughout the codebase:
// - Error logging with consistent formatting
// - Fatal error handling with context
// - Test error handling patterns
// - File validation error patterns
//
// Design Principles:
// - DRY compliance: Single implementation for common error patterns
// - Consistent error formatting across the application
// - Centralized logging behavior for easier maintenance
// - Type-safe error handling with proper context
//
// Usage Examples:
//
//	errors.LogErrorAndReturn("failed to process data", err)
//	errors.LogFatalSimple("configuration error")
//	errors.LogValidationFatal("config", "field", value, "invalid")
package errors

import (
	"fmt"

	"github.com/berrijam/mulberri/internal/utils/logger"
)

// LogErrorAndReturn logs an error with context and returns a formatted error.
//
// Args:
//   - context: Description of what operation failed
//   - err: The underlying error that occurred
//
// Returns:
//   - error: Formatted error with context for return to caller
//
// Performance: O(1) logging operation
// Side effects: Logs error message at ERROR level
func LogErrorAndReturn(context string, err error) error {
	message := fmt.Sprintf("%s: %v", context, err)
	logger.Error(message)
	return fmt.Errorf("%s: %w", context, err)
}

// LogErrorAndReturnSimple logs an error message and returns it as an error.
//
// Args:
//   - message: Error message to log and return
//
// Returns:
//   - error: Error with the provided message
//
// Performance: O(1) logging operation
// Side effects: Logs error message at ERROR level
func LogErrorAndReturnSimple(message string) error {
	logger.Error(message)
	return fmt.Errorf(message)
}

// LogFatalSimple logs a fatal error message and terminates the application.
//
// Args:
//   - message: Fatal error message
//
// Returns: Never returns (calls logger.Fatal)
// Performance: O(1) logging operation
// Side effects: Logs fatal message and terminates application
func LogFatalSimple(message string) {
	logger.Fatal(message)
}

// LogFatalWithValue logs a fatal error with a formatted value and terminates the application.
//
// Args:
//   - message: Error message template
//   - value: Value to include in the message
//
// Returns: Never returns (calls logger.Fatal)
// Performance: O(1) logging operation
// Side effects: Logs fatal message and terminates application
func LogFatalWithValue(message string, value interface{}) {
	logger.Fatal(fmt.Sprintf(message, value))
}

// ValidationError represents a validation error with context.
type ValidationError struct {
	Context string
	Field   string
	Value   interface{}
	Message string
}

// Error implements the error interface for ValidationError.
func (ve ValidationError) Error() string {
	if ve.Value != nil {
		return fmt.Sprintf("%s validation failed for %s: %s (value: %v)",
			ve.Context, ve.Field, ve.Message, ve.Value)
	}
	return fmt.Sprintf("%s validation failed for %s: %s",
		ve.Context, ve.Field, ve.Message)
}

// LogValidationFatal logs a validation error and terminates the application.
//
// Args:
//   - context: Validation context (e.g., "feature configuration")
//   - field: Field that failed validation
//   - value: Value that failed validation
//   - message: Specific validation error message
//
// Returns: Never returns (calls logger.Fatal)
// Performance: O(1) logging operation
// Side effects: Logs fatal validation error and terminates application
func LogValidationFatal(context, field string, value interface{}, message string) {
	err := ValidationError{
		Context: context,
		Field:   field,
		Value:   value,
		Message: message,
	}
	logger.Fatal(err.Error())
}
