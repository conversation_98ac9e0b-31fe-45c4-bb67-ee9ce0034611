// Package batch provides utilities for batch processing operations.
//
// This package provides helper functions for creating batch indices and dataset views:
// - Batch index creation for dataset views
// - Dataset view creation for batch processing
//
// Design Principles:
// - Simple helper functions for batch operations
// - Type-safe batch operations with generics
//
// Usage Examples:
//
//	indices := batch.CreateBatchIndices(start, end)
//	view := batch.CreateDatasetView(dataset, start, end)
package batch

import (
	"github.com/berrijam/mulberri/internal/data/dataset"
)

// CreateBatchIndices creates a slice of indices for batch processing.
//
// Args:
//   - start: Starting index (inclusive)
//   - end: Ending index (exclusive)
//
// Returns:
//   - []int: Slice of indices from start to end-1
//
// Performance: O(n) where n = end - start
// Side effects: None
func CreateBatchIndices(start, end int) []int {
	if start >= end {
		return []int{}
	}

	batchIndices := make([]int, end-start)
	for i := 0; i < end-start; i++ {
		batchIndices[i] = start + i
	}
	return batchIndices
}

// CreateDatasetView creates a dataset view for the given batch indices.
//
// Args:
//   - dataset: Source dataset to create view from
//   - start: Starting index (inclusive)
//   - end: Ending index (exclusive)
//
// Returns:
//   - *dataset.DatasetView: View containing the specified range
//
// Performance: O(n) where n = end - start for index creation
// Side effects: None
func CreateDatasetView[T comparable](dataset *dataset.Dataset[T], start, end int) *dataset.DatasetView[T] {
	batchIndices := CreateBatchIndices(start, end)
	return dataset.CreateView(batchIndices)
}
