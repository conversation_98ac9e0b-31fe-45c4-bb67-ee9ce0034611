package training

import (
	"fmt"

	"github.com/berrijam/mulberri/internal/data/dataset"
	"github.com/berrijam/mulberri/internal/utils/logger"
)

// shouldStop evaluates stopping criteria to determine if node should be a leaf.
//
// Implements the stopping criteria including maximum depth, minimum samples
// for splitting, and node purity. Returns true if any stopping condition is met.
//
// Args:
// - view: DatasetView containing samples for current node
// - targetDist: Target class distribution at current node
// - depth: Current depth in tree (0 = root)
// - effectiveMaxDepth: Calculated maximum depth limit
// - adaptiveMinSamplesSplit: Adaptive minimum samples required for splitting
// - adaptiveMinSamplesLeaf: Adaptive minimum samples required in leaf nodes
//
// Returns: True if node should be a leaf, false if splitting should continue.
// Constraints: view must be non-empty, depth must be non-negative.
// Performance: O(1) criteria evaluation.
// Relationships: Used by buildNode to determine leaf vs decision node creation.
// Side effects: Logs stopping reason when criteria met.
//
// Stopping Criteria:
// 1. Maximum depth reached (if effectiveMaxDepth > 0)
// 2. Insufficient samples for splitting (< adaptiveMinSamplesSplit)
// 3. Node is pure (all samples have same target class)
// 4. Insufficient samples for meaningful leaves (< 2 * adaptiveMinSamplesLeaf)
// 5. High purity node (entropy < 0.1) to prevent overfitting
//
// Note: Split improvement checking is handled separately in buildNode after
// the best split is found, not in the basic stopping criteria.
//
// Example: Internal method used during tree construction.
func shouldStop(
	view *dataset.DatasetView[string],
	targetDist map[string]int,
	depth int,
	effectiveMaxDepth int,
	adaptiveMinSamplesSplit int,
	adaptiveMinSamplesLeaf int,
) bool {
	samples := view.GetSize()

	// Check maximum depth (if configured)
	if effectiveMaxDepth > 0 && depth >= effectiveMaxDepth {
		logger.Debug(fmt.Sprintf("Stopping: maximum depth reached (%d >= %d)", depth, effectiveMaxDepth))
		return true
	}

	// Check minimum samples for splitting (use adaptive threshold)
	if samples < adaptiveMinSamplesSplit {
		logger.Debug(fmt.Sprintf("Stopping: insufficient samples for splitting (%d < %d)", samples, adaptiveMinSamplesSplit))
		return true
	}

	// Check node purity (all samples have same target class)
	if len(targetDist) <= 1 {
		logger.Debug("Stopping: node is pure")
		return true
	}

	// Check if we have enough samples to create meaningful child leaves
	// Need at least 2 * MinSamplesLeaf to potentially create two valid children (use adaptive threshold)
	if samples < 2*adaptiveMinSamplesLeaf {
		logger.Debug(fmt.Sprintf("Stopping: insufficient samples for meaningful child leaves (%d < %d)", samples, 2*adaptiveMinSamplesLeaf))
		return true
	}

	// Stop if node has very high purity (entropy < 0.1) to prevent overfitting
	entropy := CalculateEntropy(targetDist, samples)
	if entropy < 0.1 {
		logger.Debug("Stopping: high purity node (entropy < 0.1)")
		return true
	}

	return false
}
