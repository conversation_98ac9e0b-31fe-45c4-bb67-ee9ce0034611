package training

import (
	"testing"

	"github.com/berrijam/mulberri/internal/data/dataset"
	"github.com/berrijam/mulberri/internal/data/features"
)

// TestTreeBuilder_BasicConstruction tests basic tree builder functionality.
func TestTreeBuilder_BasicConstruction(t *testing.T) {
	// Create test configuration with new grid-search parameters
	config := BuildConfig{
		MinSamplesSplitPercent: 0.01,  // 1%
		MinSamplesLeafPercent:  0.005, // 0.5%
		MinSplitImprovement:    0.01,  // 1%
		MaxDepthAlpha:          0.8,   // Fractional multiplier
		MaxDepthExplicit:       3,     // Explicit override
		Criterion:              "entropy",
	}

	// Create tree builder
	builder, err := NewTreeBuilder(config)
	if err != nil {
		t.Fatalf("Failed to create tree builder: %v", err)
	}

	if builder == nil {
		t.Fatal("Tree builder should not be nil")
	}

	// Verify configuration
	if builder.config.MaxDepthExplicit != 3 {
		t.Errorf("Expected explicit max depth 3, got %d", builder.config.MaxDepthExplicit)
	}
	if builder.config.Criterion != "entropy" {
		t.Errorf("Expected criterion 'entropy', got %s", builder.config.Criterion)
	}
	if builder.config.MinSamplesSplitPercent != 0.01 {
		t.Errorf("Expected split percent 0.01, got %f", builder.config.MinSamplesSplitPercent)
	}
}

// TestTreeBuilder_InvalidConfiguration tests configuration validation.
func TestTreeBuilder_InvalidConfiguration(t *testing.T) {
	tests := []struct {
		name   string
		config BuildConfig
	}{
		{
			name: "negative split percent",
			config: BuildConfig{
				MinSamplesSplitPercent: -0.1, // Invalid: negative
				MinSamplesLeafPercent:  0.005,
				MinSplitImprovement:    0.01,
				MaxDepthAlpha:          0.8,
				MaxDepthExplicit:       0,
				Criterion:              "entropy",
			},
		},
		{
			name: "split percent too large",
			config: BuildConfig{
				MinSamplesSplitPercent: 1.5, // Invalid: > 1.0
				MinSamplesLeafPercent:  0.005,
				MinSplitImprovement:    0.01,
				MaxDepthAlpha:          0.8,
				MaxDepthExplicit:       0,
				Criterion:              "entropy",
			},
		},
		{
			name: "negative leaf percent",
			config: BuildConfig{
				MinSamplesSplitPercent: 0.01,
				MinSamplesLeafPercent:  -0.1, // Invalid: negative
				MinSplitImprovement:    0.01,
				MaxDepthAlpha:          0.8,
				MaxDepthExplicit:       0,
				Criterion:              "entropy",
			},
		},
		{
			name: "negative split improvement",
			config: BuildConfig{
				MinSamplesSplitPercent: 0.01,
				MinSamplesLeafPercent:  0.005,
				MinSplitImprovement:    -0.1, // Invalid: negative
				MaxDepthAlpha:          0.8,
				MaxDepthExplicit:       0,
				Criterion:              "entropy",
			},
		},
		{
			name: "zero alpha",
			config: BuildConfig{
				MinSamplesSplitPercent: 0.01,
				MinSamplesLeafPercent:  0.005,
				MinSplitImprovement:    0.01,
				MaxDepthAlpha:          0.0, // Invalid: must be positive
				MaxDepthExplicit:       0,
				Criterion:              "entropy",
			},
		},
		{
			name: "negative explicit depth",
			config: BuildConfig{
				MinSamplesSplitPercent: 0.01,
				MinSamplesLeafPercent:  0.005,
				MinSplitImprovement:    0.01,
				MaxDepthAlpha:          0.8,
				MaxDepthExplicit:       -1, // Invalid: negative
				Criterion:              "entropy",
			},
		},
		{
			name: "invalid criterion",
			config: BuildConfig{
				MinSamplesSplitPercent: 0.01,
				MinSamplesLeafPercent:  0.005,
				MinSplitImprovement:    0.01,
				MaxDepthAlpha:          0.8,
				MaxDepthExplicit:       0,
				Criterion:              "gini", // Invalid: only entropy supported
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			builder, err := NewTreeBuilder(tt.config)
			if err == nil {
				t.Errorf("Expected error for invalid configuration, but got none")
			}
			if builder != nil {
				t.Errorf("Expected nil builder for invalid configuration, but got valid builder")
			}
		})
	}
}

// TestTreeBuilder_SimpleTree tests building a simple decision tree.
func TestTreeBuilder_SimpleTree(t *testing.T) {
	// Create test dataset
	dataset := dataset.NewDataset[string](100)

	// Add age feature (numerical)
	ages := make([]int64, 100)
	ageNulls := make([]bool, 100)
	for i := 0; i < 100; i++ {
		if i < 50 {
			ages[i] = int64(20 + i/5) // Ages 20-29
		} else {
			ages[i] = int64(30 + (i-50)/5) // Ages 30-39
		}
		ageNulls[i] = false
	}
	dataset.AddIntColumn("age", ages, ageNulls)

	// Add target values
	for i := 0; i < 100; i++ {
		if i < 50 {
			dataset.AddTarget("young")
		} else {
			dataset.AddTarget("old")
		}
	}

	// Create feature types
	featureTypes := map[string]features.FeatureType{
		"age": features.IntegerFeature,
	}

	// Create all indices view
	allIndices := make([]int, 100)
	for i := range allIndices {
		allIndices[i] = i
	}
	view := dataset.CreateView(allIndices)

	// Create tree builder
	config := BuildConfig{
		MinSamplesSplitPercent: 0.1,  // 10% for test (10 samples out of 100)
		MinSamplesLeafPercent:  0.05, // 5% for test (5 samples out of 100)
		MinSplitImprovement:    0.0,  // Allow any improvement for test
		MaxDepthAlpha:          0.8,
		MaxDepthExplicit:       5, // Explicit max depth
		Criterion:              "entropy",
	}

	builder, err := NewTreeBuilder(config)
	if err != nil {
		t.Fatalf("Failed to create tree builder: %v", err)
	}

	// Build tree
	tree, err := builder.BuildTree(view, featureTypes, "target")
	if err != nil {
		t.Fatalf("Failed to build tree: %v", err)
	}

	if tree == nil {
		t.Fatal("Tree should not be nil")
	}

	// Verify tree properties
	if tree.Root == nil {
		t.Error("Tree root should not be nil")
	}

	if len(tree.Features) != 1 {
		t.Errorf("Expected 1 feature, got %d", len(tree.Features))
	}

	if len(tree.Classes) != 2 {
		t.Errorf("Expected 2 classes, got %d", len(tree.Classes))
	}

	if tree.Metadata == nil {
		t.Error("Tree metadata should not be nil")
	}

	// Verify metadata
	if tree.Metadata.Algorithm != "C4.5" {
		t.Errorf("Expected algorithm 'C4.5', got %s", tree.Metadata.Algorithm)
	}

	if tree.Metadata.TotalNodes <= 0 {
		t.Errorf("Expected positive total nodes, got %d", tree.Metadata.TotalNodes)
	}

	if tree.Metadata.LeafNodes <= 0 {
		t.Errorf("Expected positive leaf nodes, got %d", tree.Metadata.LeafNodes)
	}

	t.Logf("Tree built successfully: %d total nodes, %d leaf nodes, max depth %d",
		tree.Metadata.TotalNodes, tree.Metadata.LeafNodes, tree.Metadata.MaxDepth)
}

// TestTreeBuilder_EmptyDataset tests error handling for empty dataset.
func TestTreeBuilder_EmptyDataset(t *testing.T) {
	config := BuildConfig{
		MinSamplesSplitPercent: 0.01,
		MinSamplesLeafPercent:  0.005,
		MinSplitImprovement:    0.0,
		MaxDepthAlpha:          0.8,
		MaxDepthExplicit:       5,
		Criterion:              "entropy",
	}

	builder, err := NewTreeBuilder(config)
	if err != nil {
		t.Fatalf("Failed to create tree builder: %v", err)
	}

	// Create empty dataset view
	dataset := dataset.NewDataset[string](0)
	view := dataset.CreateView([]int{})

	featureTypes := map[string]features.FeatureType{
		"age": features.IntegerFeature,
	}

	// Should fail with empty dataset
	tree, err := builder.BuildTree(view, featureTypes, "target")
	if err == nil {
		t.Error("Expected error for empty dataset, but got none")
	}
	if tree != nil {
		t.Error("Expected nil tree for empty dataset, but got valid tree")
	}
}

// TestTreeBuilder_CalculateEffectiveMaxDepth tests the effective max depth calculation.
func TestTreeBuilder_CalculateEffectiveMaxDepth(t *testing.T) {
	tests := []struct {
		name          string
		alpha         float64
		explicit      int
		numFeatures   int
		expectedDepth int
	}{
		{
			name:          "explicit depth overrides alpha",
			alpha:         0.8,
			explicit:      5,
			numFeatures:   35,
			expectedDepth: 5, // Should use explicit value
		},
		{
			name:          "alpha calculation with 35 features",
			alpha:         0.8,
			explicit:      0, // No explicit override
			numFeatures:   35,
			expectedDepth: 4, // 0.8 * log2(35) ≈ 0.8 * 5.13 ≈ 4.1 → 4 (rounded)
		},
		{
			name:          "alpha calculation with 10 features",
			alpha:         2.0,
			explicit:      0,
			numFeatures:   10,
			expectedDepth: 7, // 2.0 * log2(10) ≈ 2.0 * 3.32 ≈ 6.6 → 7
		},
		{
			name:          "minimum depth of 1",
			alpha:         0.5,
			explicit:      0,
			numFeatures:   2,
			expectedDepth: 1, // 0.5 * log2(2) = 0.5 * 1 = 0.5 → 1 (minimum)
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			config := BuildConfig{
				MinSamplesSplitPercent: 0.01,
				MinSamplesLeafPercent:  0.005,
				MinSplitImprovement:    0.01,
				MaxDepthAlpha:          tt.alpha,
				MaxDepthExplicit:       tt.explicit,
				Criterion:              "entropy",
			}

			builder, err := NewTreeBuilder(config)
			if err != nil {
				t.Fatalf("Failed to create tree builder: %v", err)
			}

			actualDepth := builder.calculateEffectiveMaxDepth(tt.numFeatures)
			if actualDepth != tt.expectedDepth {
				t.Errorf("Expected depth %d, got %d", tt.expectedDepth, actualDepth)
			}
		})
	}
}

// TestTreeBuilder_CalculateAdaptiveThresholds tests adaptive threshold calculation.
func TestTreeBuilder_CalculateAdaptiveThresholds(t *testing.T) {
	tests := []struct {
		name             string
		splitPercent     float64
		leafPercent      float64
		datasetSize      int
		expectedMinSplit int
		expectedMinLeaf  int
	}{
		{
			name:             "1000 samples with 1% split, 0.5% leaf",
			splitPercent:     0.01,
			leafPercent:      0.005,
			datasetSize:      1000,
			expectedMinSplit: 10, // 1% of 1000 = 10
			expectedMinLeaf:  5,  // 0.5% of 1000 = 5
		},
		{
			name:             "100 samples with 2% split, 1% leaf",
			splitPercent:     0.02,
			leafPercent:      0.01,
			datasetSize:      100,
			expectedMinSplit: 2, // 2% of 100 = 2
			expectedMinLeaf:  1, // 1% of 100 = 1
		},
		{
			name:             "small dataset with minimum constraints",
			splitPercent:     0.001, // 0.1%
			leafPercent:      0.001, // 0.1%
			datasetSize:      50,
			expectedMinSplit: 2, // max(ceil(50 * 0.001), 2) = max(1, 2) = 2
			expectedMinLeaf:  1, // max(ceil(50 * 0.001), 1) = max(1, 1) = 1
		},
		{
			name:             "large dataset",
			splitPercent:     0.005, // 0.5%
			leafPercent:      0.002, // 0.2%
			datasetSize:      10000,
			expectedMinSplit: 50, // 0.5% of 10000 = 50
			expectedMinLeaf:  20, // 0.2% of 10000 = 20
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			config := BuildConfig{
				MinSamplesSplitPercent: tt.splitPercent,
				MinSamplesLeafPercent:  tt.leafPercent,
				MinSplitImprovement:    0.01,
				MaxDepthAlpha:          0.8,
				MaxDepthExplicit:       0,
				Criterion:              "entropy",
			}

			builder, err := NewTreeBuilder(config)
			if err != nil {
				t.Fatalf("Failed to create tree builder: %v", err)
			}

			builder.calculateAdaptiveThresholds(tt.datasetSize)

			if builder.adaptiveMinSamplesSplit != tt.expectedMinSplit {
				t.Errorf("Expected min split %d, got %d", tt.expectedMinSplit, builder.adaptiveMinSamplesSplit)
			}
			if builder.adaptiveMinSamplesLeaf != tt.expectedMinLeaf {
				t.Errorf("Expected min leaf %d, got %d", tt.expectedMinLeaf, builder.adaptiveMinSamplesLeaf)
			}
		})
	}
}
