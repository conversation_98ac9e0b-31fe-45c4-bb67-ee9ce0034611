// Package training provides split evaluation functionality for decision trees.
//
// This module focuses specifically on evaluating potential splits for different
// feature types, providing the core splitting logic without tree building overhead.
//
// Design Principles:
// - Focused on split evaluation only
// - Support for numerical and categorical features
// - Memory-efficient view-based operations
// - Clean separation of concerns
package training

import (
	"fmt"

	"github.com/berrijam/mulberri/internal/data/dataset"
	"github.com/berrijam/mulberri/internal/data/features"
	"github.com/berrijam/mulberri/internal/utils/logger"
)

// SplitEvaluationResult contains the results of evaluating splits for a feature.
//
// Provides comprehensive information about potential splits including
// the best candidate and all evaluated options for analysis.
//
// Performance: Lightweight result container
// Relationships: Returned by split evaluation functions
// Side effects: None (immutable result)
type SplitEvaluationResult struct {
	BestCandidate *SplitCandidate      // Best split found (highest gain)
	AllCandidates []SplitCandidate     // All evaluated split candidates
	FeatureName   string               // Name of the feature evaluated
	FeatureType   features.FeatureType // Type of the feature evaluated
	BaseImpurity  float64              // Original impurity before splitting
}

// SplitDatasetViewByBranch applies a split candidate to create multiple child views.
//
// Unified function that handles both binary and n-ary splits by partitioning samples
// according to the split condition. Binary splits are treated as a special case of
// n-ary splits where n=2.
//
// Args:
// - view: Parent DatasetView to split
// - dataset: Full dataset for accessing feature values
// - split: SplitCandidate containing split information
//
// Returns: Map of branch values to child DatasetViews
// Constraints: Split must be valid, feature must exist in dataset
// Performance: O(n) where n is number of samples in view
// Relationships: Bridges split evaluation and view partitioning
// Side effects: Creates new DatasetView instances
//
// Split Logic:
//   - Numerical: Creates 2 branches ("left" for ≤ threshold, "right" for > threshold)
//   - Categorical N-ary: Creates n branches (one per unique categorical value)
//     Binary categorical splits are treated as n-ary splits where n=2
//
// Example:
//
//	childViews := SplitDatasetViewByBranch(parentView, dataset, bestSplit)
//	for branchValue, childView := range childViews {
//	    fmt.Printf("Branch %v: %d samples", branchValue, childView.GetSize())
//	}
func SplitDatasetViewByBranch[T comparable](
	view *dataset.DatasetView[T],
	ds *dataset.Dataset[T],
	split *SplitCandidate,
) map[interface{}]*dataset.DatasetView[T] {

	if split == nil {
		logger.Error("Split candidate cannot be nil")
		return nil
	}

	// Handle n-ary categorical splits using pre-computed partitions
	if split.Type == CategoricalSplit {
		childViews := make(map[interface{}]*dataset.DatasetView[T])

		// Create child views from pre-computed partitions
		for value, indices := range split.ChildPartitions {
			if len(indices) > 0 {
				childView := ds.CreateView(indices)
				if childView != nil {
					childViews[value] = childView
				}
			}
		}
		return childViews
	}

	// Handle binary numerical splits only (categorical splits are always n-ary now)
	if split.Type == NumericalSplit {
		var leftIndices, rightIndices []int

		// Get the feature column
		column := ds.GetColumn(split.FeatureName)
		if column == nil {
			logger.Error(fmt.Sprintf("Feature column not found: %s", split.FeatureName))
			return nil
		}

		// Cache active indices outside the loop to avoid repeated function calls
		activeIndices := view.GetActiveIndices()

		// Partition samples for numerical splits
		for i := 0; i < view.GetSize(); i++ {
			value := view.GetFeatureValue(i, split.FeatureName)
			if value == nil {
				continue // Skip missing values
			}

			goesLeft := false
			if split.Threshold != nil {
				if numValue := extractNumericalValue(value); numValue != nil {
					goesLeft = *numValue <= *split.Threshold
				}
			}

			// Get physical index for this logical index
			physicalIndex := activeIndices[i]
			if goesLeft {
				leftIndices = append(leftIndices, physicalIndex)
			} else {
				rightIndices = append(rightIndices, physicalIndex)
			}
		}

		// Create child views for binary numerical splits
		childViews := make(map[interface{}]*dataset.DatasetView[T])

		if len(leftIndices) > 0 {
			leftView := ds.CreateView(leftIndices)
			if leftView != nil {
				childViews["lte"] = leftView
			}
		}

		if len(rightIndices) > 0 {
			rightView := ds.CreateView(rightIndices)
			if rightView != nil {
				childViews["gt"] = rightView
			}
		}

		return childViews
	}

	// If we reach here, it's an unsupported split type
	logger.Error(fmt.Sprintf("Unsupported split type: %v", split.Type))
	return nil
}

// extractNumericalValue safely extracts a numerical value from an interface.
//
// Helper function for converting interface{} values to float64 for
// numerical split evaluation.
//
// Args:
// - value: Interface value to convert
//
// Returns: Pointer to float64 value or nil if conversion fails
// Performance: O(1) type assertion
// Relationships: Helper for numerical split application
// Side effects: None (pure function)
func extractNumericalValue(value interface{}) *float64 {
	switch v := value.(type) {
	case *int64:
		if v != nil {
			result := float64(*v)
			return &result
		}
	case *float64:
		return v
	}
	return nil
}
