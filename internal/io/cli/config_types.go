package cli

import (
	"fmt"
	"strings"

	"github.com/berrijam/mulberri/internal/utils/logger"
)

// TrainingConfig holds configuration specific to training operations.
//
// Contains all parameters needed for training decision trees including
// file paths, algorithm parameters, and feature specifications.
// Initialized with defaults from config package.
type TrainingConfig struct {
	// File paths
	InputFile       string // CSV file with training data
	TargetCol       string // Name of target column
	OutputFile      string // Path to save trained model
	FeatureInfoFile string // Feature metadata YAML file

	// Grid-search optimizable parameters
	MinSamplesSplitPercent float64 // Percentage of dataset for minimum split samples (default: 1%)
	MinSamplesLeafPercent  float64 // Percentage of dataset for minimum leaf samples (default: 0.5%)
	MinSplitImprovement    float64 // Minimum gain ratio improvement required for splits (default: 0.01)

	// Max depth configuration (supports both fractional and explicit)
	MaxDepthAlpha    float64 // Fractional multiplier: max_depth = alpha * log2(features) (default: 0.8)
	MaxDepthExplicit int     // Explicit max depth value (overrides alpha if > 0)

	// Algorithm configuration
	Criterion string // Splitting criterion (entropy only)

	// Options
	Verbose bool // Enable detailed output
}

// PredictionConfig holds configuration specific to prediction operations.
//
// Contains parameters needed for making predictions with trained models
// including input data, model file, and output specifications.
// Consolidated from internal/config/prediction.go to eliminate redundancy.
type PredictionConfig struct {
	// File paths
	InputFile  string // CSV file with data to predict
	ModelFile  string // Trained model file (.dt format)
	OutputFile string // Path to save predictions

	// Output configuration
	IncludeHeaders bool // Include column headers in output (default: true)

	// Processing options
	BatchSize int  // Batch size for processing large datasets (default: 1000)
	Verbose   bool // Enable detailed output and logging (default: false)

	// Output options
	DetailedOutput bool // Enable detailed prediction output with decision paths and reasoning (default: false)

}

// NewPredictionConfig creates a new prediction configuration with default values.
//
// This constructor initializes a prediction configuration with sensible defaults
// that can be overridden based on user input or specific requirements.
//
// Returns:
//   - *PredictionConfig: Configuration instance with default values set
//
// Default values follow the requirements specification and best practices
// for prediction operations in the Mulberri decision tree system.
func NewPredictionConfig() *PredictionConfig {
	return &PredictionConfig{
		InputFile:  "",
		ModelFile:  "",
		OutputFile: "",

		IncludeHeaders: true,

		// Processing defaults
		BatchSize: 1000,
		Verbose:   false,

		// Detailed output defaults
		DetailedOutput: false,
	}
}

// Validate performs comprehensive validation for training configuration.
//
// Checks file existence, parameter ranges, and required fields.
// Side effects: Exits on first validation error encountered for faster feedback.
func (c *TrainingConfig) Validate() {
	validateInputFile(c.InputFile)

	if strings.TrimSpace(c.TargetCol) == "" {
		logger.Fatal("target column is required")
	}

	if strings.TrimSpace(c.OutputFile) == "" {
		logger.Fatal("output file is required")
	}

	// Validate grid-search parameters
	if c.MinSamplesSplitPercent < 0.0 || c.MinSamplesSplitPercent > 1.0 {
		logger.Fatal(fmt.Sprintf("min samples split percent must be between 0.0 and 1.0: %f", c.MinSamplesSplitPercent))
	}

	if c.MinSamplesLeafPercent < 0.0 || c.MinSamplesLeafPercent > 1.0 {
		logger.Fatal(fmt.Sprintf("min samples leaf percent must be between 0.0 and 1.0: %f", c.MinSamplesLeafPercent))
	}

	if c.MinSplitImprovement < 0.0 {
		logger.Fatal(fmt.Sprintf("min split improvement must be non-negative: %f", c.MinSplitImprovement))
	}
	// Validate max depth parameters
	if c.MaxDepthExplicit < 0 {
		logger.Fatal(fmt.Sprintf("explicit max depth must be non-negative: %d", c.MaxDepthExplicit))
	}

	if c.MaxDepthAlpha <= 0.0 {
		logger.Fatal(fmt.Sprintf("max depth alpha must be positive: %f", c.MaxDepthAlpha))
	}

	// Prevent both alpha and explicit depth from being set
	if c.MaxDepthExplicit > 0 && c.MaxDepthAlpha != 0.8 {
		logger.Fatal("cannot specify both --max-depth-alpha and --max-depth flags together")
	}

	criterion := strings.TrimSpace(c.Criterion)
	if criterion != "entropy" {
		logger.Fatal(fmt.Sprintf("only entropy criterion supported, got: %s", c.Criterion))
	}

	// Validate required feature info file
	if strings.TrimSpace(c.FeatureInfoFile) == "" {
		logger.Fatal("feature info file is required")
	}
	validateYAMLFile(c.FeatureInfoFile)
}

// Validate performs comprehensive validation for prediction configuration.
//
// Checks file existence and required parameters for prediction workflow.
// Uses existing CLI validation helpers for file validation and adds
// configuration consistency checks.
// Side effects: Exits on first validation error encountered for faster feedback.
func (c *PredictionConfig) Validate() {
	validateInputFile(c.InputFile)

	if strings.TrimSpace(c.ModelFile) == "" {
		logger.Fatal("model file is required")
	}

	validateModelFile(c.ModelFile)

	if strings.TrimSpace(c.OutputFile) == "" {
		logger.Fatal("output file is required")
	}

}
