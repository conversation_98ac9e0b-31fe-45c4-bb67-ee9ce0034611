package cli

import (
	"testing"

	"github.com/berrijam/mulberri/internal/training"
)

// TestCreateBuildConfig tests the conversion from CLI config to build config.
func TestCreateBuildConfig(t *testing.T) {
	tests := []struct {
		name           string
		cliConfig      TrainingConfig
		expectedConfig training.BuildConfig
	}{
		{
			name: "default configuration",
			cliConfig: TrainingConfig{
				MinSamplesSplitPercent: 0.01,
				MinSamplesLeafPercent:  0.005,
				MinSplitImprovement:    0.01,
				MaxDepthAlpha:          0.8,
				MaxDepthExplicit:       0,
				Criterion:              "entropy",
				Verbose:                false,
			},
			expectedConfig: training.BuildConfig{
				MinSamplesSplitPercent: 0.01,
				MinSamplesLeafPercent:  0.005,
				MinSplitImprovement:    0.01,
				MaxDepthAlpha:          0.8,
				MaxDepthExplicit:       0,
				Criterion:              "entropy",
			},
		},
		{
			name: "custom configuration with explicit depth",
			cliConfig: TrainingConfig{
				MinSamplesSplitPercent: 0.02,
				MinSamplesLeafPercent:  0.01,
				MinSplitImprovement:    0.005,
				MaxDepthAlpha:          2.0,
				MaxDepthExplicit:       5,
				Criterion:              "entropy",
				Verbose:                true,
			},
			expectedConfig: training.BuildConfig{
				MinSamplesSplitPercent: 0.02,
				MinSamplesLeafPercent:  0.01,
				MinSplitImprovement:    0.005,
				MaxDepthAlpha:          2.0,
				MaxDepthExplicit:       5,
				Criterion:              "entropy",
			},
		},
		{
			name: "aggressive parameters",
			cliConfig: TrainingConfig{
				MinSamplesSplitPercent: 0.001,
				MinSamplesLeafPercent:  0.001,
				MinSplitImprovement:    0.001,
				MaxDepthAlpha:          3.0,
				MaxDepthExplicit:       0,
				Criterion:              "entropy",
				Verbose:                false,
			},
			expectedConfig: training.BuildConfig{
				MinSamplesSplitPercent: 0.001,
				MinSamplesLeafPercent:  0.001,
				MinSplitImprovement:    0.001,
				MaxDepthAlpha:          3.0,
				MaxDepthExplicit:       0,
				Criterion:              "entropy",
			},
		},
		{
			name: "conservative parameters",
			cliConfig: TrainingConfig{
				MinSamplesSplitPercent: 0.05,
				MinSamplesLeafPercent:  0.02,
				MinSplitImprovement:    0.05,
				MaxDepthAlpha:          0.75,
				MaxDepthExplicit:       3,
				Criterion:              "entropy",
				Verbose:                false,
			},
			expectedConfig: training.BuildConfig{
				MinSamplesSplitPercent: 0.05,
				MinSamplesLeafPercent:  0.02,
				MinSplitImprovement:    0.05,
				MaxDepthAlpha:          0.75,
				MaxDepthExplicit:       3,
				Criterion:              "entropy",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			buildConfig := createBuildConfig(&tt.cliConfig)

			// Verify all grid-search parameters are mapped correctly
			if buildConfig.MinSamplesSplitPercent != tt.expectedConfig.MinSamplesSplitPercent {
				t.Errorf("Expected split percent %f, got %f", tt.expectedConfig.MinSamplesSplitPercent, buildConfig.MinSamplesSplitPercent)
			}
			if buildConfig.MinSamplesLeafPercent != tt.expectedConfig.MinSamplesLeafPercent {
				t.Errorf("Expected leaf percent %f, got %f", tt.expectedConfig.MinSamplesLeafPercent, buildConfig.MinSamplesLeafPercent)
			}
			if buildConfig.MinSplitImprovement != tt.expectedConfig.MinSplitImprovement {
				t.Errorf("Expected split improvement %f, got %f", tt.expectedConfig.MinSplitImprovement, buildConfig.MinSplitImprovement)
			}
			if buildConfig.MaxDepthAlpha != tt.expectedConfig.MaxDepthAlpha {
				t.Errorf("Expected alpha %f, got %f", tt.expectedConfig.MaxDepthAlpha, buildConfig.MaxDepthAlpha)
			}
			if buildConfig.MaxDepthExplicit != tt.expectedConfig.MaxDepthExplicit {
				t.Errorf("Expected explicit depth %d, got %d", tt.expectedConfig.MaxDepthExplicit, buildConfig.MaxDepthExplicit)
			}
			if buildConfig.Criterion != tt.expectedConfig.Criterion {
				t.Errorf("Expected criterion %s, got %s", tt.expectedConfig.Criterion, buildConfig.Criterion)
			}
		})
	}
}

// TestCreateBuildConfig_GridSearchParameters tests that grid-search parameters are properly mapped.
func TestCreateBuildConfig_GridSearchParameters(t *testing.T) {
	// Test boundary values for grid-search parameters
	tests := []struct {
		name      string
		splitPct  float64
		leafPct   float64
		improve   float64
		alpha     float64
		explicit  int
	}{
		{"minimum values", 0.001, 0.001, 0.001, 0.1, 0},
		{"default values", 0.01, 0.005, 0.01, 0.8, 0},
		{"maximum values", 0.1, 0.05, 0.1, 5.0, 20},
		{"explicit depth override", 0.01, 0.005, 0.01, 0.8, 10},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cliConfig := TrainingConfig{
				MinSamplesSplitPercent: tt.splitPct,
				MinSamplesLeafPercent:  tt.leafPct,
				MinSplitImprovement:    tt.improve,
				MaxDepthAlpha:          tt.alpha,
				MaxDepthExplicit:       tt.explicit,
				Criterion:              "entropy",
				Verbose:                false,
			}

			buildConfig := createBuildConfig(&cliConfig)

			// Verify exact mapping of grid-search parameters
			if buildConfig.MinSamplesSplitPercent != tt.splitPct {
				t.Errorf("Split percent not mapped correctly: expected %f, got %f", tt.splitPct, buildConfig.MinSamplesSplitPercent)
			}
			if buildConfig.MinSamplesLeafPercent != tt.leafPct {
				t.Errorf("Leaf percent not mapped correctly: expected %f, got %f", tt.leafPct, buildConfig.MinSamplesLeafPercent)
			}
			if buildConfig.MinSplitImprovement != tt.improve {
				t.Errorf("Split improvement not mapped correctly: expected %f, got %f", tt.improve, buildConfig.MinSplitImprovement)
			}
			if buildConfig.MaxDepthAlpha != tt.alpha {
				t.Errorf("Alpha not mapped correctly: expected %f, got %f", tt.alpha, buildConfig.MaxDepthAlpha)
			}
			if buildConfig.MaxDepthExplicit != tt.explicit {
				t.Errorf("Explicit depth not mapped correctly: expected %d, got %d", tt.explicit, buildConfig.MaxDepthExplicit)
			}
		})
	}
}

// TestCreateBuildConfig_CriterionMapping tests that criterion is properly mapped.
func TestCreateBuildConfig_CriterionMapping(t *testing.T) {
	cliConfig := TrainingConfig{
		MinSamplesSplitPercent: 0.01,
		MinSamplesLeafPercent:  0.005,
		MinSplitImprovement:    0.01,
		MaxDepthAlpha:          0.8,
		MaxDepthExplicit:       0,
		Criterion:              "entropy",
		Verbose:                false,
	}

	buildConfig := createBuildConfig(&cliConfig)

	if buildConfig.Criterion != "entropy" {
		t.Errorf("Expected criterion 'entropy', got %s", buildConfig.Criterion)
	}
}
