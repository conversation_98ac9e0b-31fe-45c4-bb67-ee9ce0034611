package cli

import (
	"testing"

	"github.com/berrijam/mulberri/internal/config"
)

// TestTrainingConfig_ValidateParameters tests parameter validation logic.
// Note: The actual Validate() method calls log.Fatal() on errors, so we test
// the underlying validation logic by checking parameter ranges directly.
func TestTrainingConfig_ValidateParameters(t *testing.T) {
	tests := []struct {
		name        string
		config      TrainingConfig
		expectValid bool
	}{
		{
			name: "valid configuration",
			config: TrainingConfig{
				MinSamplesSplitPercent: 0.01,
				MinSamplesLeafPercent:  0.005,
				MinSplitImprovement:    0.01,
				MaxDepthAlpha:          0.8,
				MaxDepthExplicit:       0,
				Criterion:              "entropy",
				Verbose:                false,
			},
			expectValid: true,
		},
		{
			name: "negative split percent",
			config: TrainingConfig{
				MinSamplesSplitPercent: -0.1, // Invalid
				MinSamplesLeafPercent:  0.005,
				MinSplitImprovement:    0.01,
				MaxDepthAlpha:          0.8,
				MaxDepthExplicit:       0,
				Criterion:              "entropy",
				Verbose:                false,
			},
			expectValid: false,
		},
		{
			name: "split percent too large",
			config: TrainingConfig{
				MinSamplesSplitPercent: 1.5, // Invalid: > 1.0
				MinSamplesLeafPercent:  0.005,
				MinSplitImprovement:    0.01,
				MaxDepthAlpha:          0.8,
				MaxDepthExplicit:       0,
				Criterion:              "entropy",
				Verbose:                false,
			},
			expectValid: false,
		},
		{
			name: "negative leaf percent",
			config: TrainingConfig{
				MinSamplesSplitPercent: 0.01,
				MinSamplesLeafPercent:  -0.1, // Invalid
				MinSplitImprovement:    0.01,
				MaxDepthAlpha:          0.8,
				MaxDepthExplicit:       0,
				Criterion:              "entropy",
				Verbose:                false,
			},
			expectValid: false,
		},
		{
			name: "leaf percent too large",
			config: TrainingConfig{
				MinSamplesSplitPercent: 0.01,
				MinSamplesLeafPercent:  1.2, // Invalid: > 1.0
				MinSplitImprovement:    0.01,
				MaxDepthAlpha:          0.8,
				MaxDepthExplicit:       0,
				Criterion:              "entropy",
				Verbose:                false,
			},
			expectValid: false,
		},
		{
			name: "negative split improvement",
			config: TrainingConfig{
				MinSamplesSplitPercent: 0.01,
				MinSamplesLeafPercent:  0.005,
				MinSplitImprovement:    -0.1, // Invalid
				MaxDepthAlpha:          0.8,
				MaxDepthExplicit:       0,
				Criterion:              "entropy",
				Verbose:                false,
			},
			expectValid: false,
		},
		{
			name: "zero alpha",
			config: TrainingConfig{
				MinSamplesSplitPercent: 0.01,
				MinSamplesLeafPercent:  0.005,
				MinSplitImprovement:    0.01,
				MaxDepthAlpha:          0.0, // Invalid: must be positive
				MaxDepthExplicit:       0,
				Criterion:              "entropy",
				Verbose:                false,
			},
			expectValid: false,
		},
		{
			name: "negative explicit depth",
			config: TrainingConfig{
				MinSamplesSplitPercent: 0.01,
				MinSamplesLeafPercent:  0.005,
				MinSplitImprovement:    0.01,
				MaxDepthAlpha:          0.8,
				MaxDepthExplicit:       -1, // Invalid
				Criterion:              "entropy",
				Verbose:                false,
			},
			expectValid: false,
		},
		{
			name: "invalid criterion",
			config: TrainingConfig{
				MinSamplesSplitPercent: 0.01,
				MinSamplesLeafPercent:  0.005,
				MinSplitImprovement:    0.01,
				MaxDepthAlpha:          0.8,
				MaxDepthExplicit:       0,
				Criterion:              "gini", // Invalid: only entropy supported
				Verbose:                false,
			},
			expectValid: false,
		},
		{
			name: "explicit depth overrides alpha",
			config: TrainingConfig{
				MinSamplesSplitPercent: 0.01,
				MinSamplesLeafPercent:  0.005,
				MinSplitImprovement:    0.01,
				MaxDepthAlpha:          0.8,
				MaxDepthExplicit:       5, // Valid explicit override
				Criterion:              "entropy",
				Verbose:                false,
			},
			expectValid: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Test parameter validation logic directly since Validate() calls log.Fatal()
			isValid := isConfigValid(tt.config)
			if tt.expectValid && !isValid {
				t.Error("Expected configuration to be valid, but it was invalid")
			}
			if !tt.expectValid && isValid {
				t.Error("Expected configuration to be invalid, but it was valid")
			}
		})
	}
}

// isConfigValid checks if a configuration has valid parameter ranges.
// This mimics the validation logic without calling log.Fatal().
func isConfigValid(config TrainingConfig) bool {
	// Check percentage ranges
	if config.MinSamplesSplitPercent < 0.0 || config.MinSamplesSplitPercent > 1.0 {
		return false
	}
	if config.MinSamplesLeafPercent < 0.0 || config.MinSamplesLeafPercent > 1.0 {
		return false
	}

	// Check split improvement
	if config.MinSplitImprovement < 0.0 {
		return false
	}

	// Check alpha
	if config.MaxDepthAlpha <= 0.0 {
		return false
	}

	// Check explicit depth
	if config.MaxDepthExplicit < 0 {
		return false
	}

	// Check criterion
	if config.Criterion != "entropy" {
		return false
	}

	return true
}

// TestTrainingConfig_DefaultValues tests that default values are set correctly.
func TestTrainingConfig_DefaultValues(t *testing.T) {
	config := TrainingConfig{
		MinSamplesSplitPercent: config.DefaultMinSamplesSplitPercent,
		MinSamplesLeafPercent:  config.DefaultMinSamplesLeafPercent,
		MinSplitImprovement:    config.DefaultMinSplitImprovement,
		MaxDepthAlpha:          config.DefaultMaxDepthAlpha,
		MaxDepthExplicit:       0,
		Criterion:              config.DefaultCriterion,
		Verbose:                false,
	}

	// Test that defaults are valid
	if !isConfigValid(config) {
		t.Error("Default configuration should be valid")
	}

	// Test specific default values
	if config.MinSamplesSplitPercent != 0.01 {
		t.Errorf("Expected default split percent 0.01, got %f", config.MinSamplesSplitPercent)
	}
	if config.MinSamplesLeafPercent != 0.005 {
		t.Errorf("Expected default leaf percent 0.005, got %f", config.MinSamplesLeafPercent)
	}
	if config.MinSplitImprovement != 0.01 {
		t.Errorf("Expected default split improvement 0.01, got %f", config.MinSplitImprovement)
	}
	if config.MaxDepthAlpha != 0.8 {
		t.Errorf("Expected default alpha 0.8, got %f", config.MaxDepthAlpha)
	}
	if config.Criterion != "entropy" {
		t.Errorf("Expected default criterion 'entropy', got %s", config.Criterion)
	}
}
