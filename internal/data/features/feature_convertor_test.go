package features

import (
	"testing"
)

// TestConvertValue tests the ConvertValue function with various inputs and feature types.
func TestConvertValue(t *testing.T) {
	tests := []struct {
		name         string
		rawValue     string
		featureType  FeatureType
		expectedVal  any
		expectedType string
	}{
		// Integer feature tests
		{
			name:         "valid integer conversion",
			rawValue:     "123",
			featureType:  IntegerFeature,
			expectedVal:  int64(123),
			expectedType: "int64",
		},
		{
			name:         "negative integer conversion",
			rawValue:     "-456",
			featureType:  IntegerFeature,
			expectedVal:  int64(-456),
			expectedType: "int64",
		},
		{
			name:         "zero integer conversion",
			rawValue:     "0",
			featureType:  IntegerFeature,
			expectedVal:  int64(0),
			expectedType: "int64",
		},
		{
			name:         "integer with whitespace",
			rawValue:     "  789  ",
			featureType:  IntegerFeature,
			expectedVal:  int64(789),
			expectedType: "int64",
		},

		// Float feature tests
		{
			name:         "valid float conversion",
			rawValue:     "123.45",
			featureType:  FloatFeature,
			expectedVal:  float64(123.45),
			expectedType: "float64",
		},
		{
			name:         "negative float conversion",
			rawValue:     "-456.78",
			featureType:  FloatFeature,
			expectedVal:  float64(-456.78),
			expectedType: "float64",
		},
		{
			name:         "integer as float",
			rawValue:     "123",
			featureType:  FloatFeature,
			expectedVal:  float64(123),
			expectedType: "float64",
		},
		{
			name:         "zero float conversion",
			rawValue:     "0.0",
			featureType:  FloatFeature,
			expectedVal:  float64(0.0),
			expectedType: "float64",
		},
		{
			name:         "float with whitespace",
			rawValue:     "  3.14159  ",
			featureType:  FloatFeature,
			expectedVal:  float64(3.14159),
			expectedType: "float64",
		},

		// String feature tests
		{
			name:         "valid string conversion",
			rawValue:     "sunny",
			featureType:  StringFeature,
			expectedVal:  "sunny",
			expectedType: "string",
		},
		{
			name:         "string with whitespace",
			rawValue:     "  cloudy  ",
			featureType:  StringFeature,
			expectedVal:  "cloudy",
			expectedType: "string",
		},
		{
			name:         "numeric string",
			rawValue:     "123",
			featureType:  StringFeature,
			expectedVal:  "123",
			expectedType: "string",
		},

		// Edge cases - missing values
		{
			name:         "empty string",
			rawValue:     "",
			featureType:  IntegerFeature,
			expectedVal:  nil,
			expectedType: "nil",
		},
		{
			name:         "whitespace only",
			rawValue:     "   ",
			featureType:  FloatFeature,
			expectedVal:  nil,
			expectedType: "nil",
		},
		{
			name:         "empty string for string feature",
			rawValue:     "   ",
			featureType:  StringFeature,
			expectedVal:  nil,
			expectedType: "nil",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := ConvertValue(tt.rawValue, tt.featureType)

			if result != tt.expectedVal {
				t.Errorf("ConvertValue() = %v (%T), expected %v (%T)", result, result, tt.expectedVal, tt.expectedVal)
			}

			// Verify type
			switch tt.expectedType {
			case "int64":
				if _, ok := result.(int64); !ok {
					t.Errorf("ConvertValue() result type = %T, expected int64", result)
				}
			case "float64":
				if _, ok := result.(float64); !ok {
					t.Errorf("ConvertValue() result type = %T, expected float64", result)
				}
			case "string":
				if _, ok := result.(string); !ok {
					t.Errorf("ConvertValue() result type = %T, expected string", result)
				}
			case "nil":
				if result != nil {
					t.Errorf("ConvertValue() result = %v, expected nil", result)
				}
			}
		})
	}
}
