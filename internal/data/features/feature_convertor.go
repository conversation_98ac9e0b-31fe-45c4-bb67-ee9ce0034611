package features

import (
	"fmt"
	"strconv"
	"strings"

	"github.com/berrijam/mulberri/internal/utils/logger"
)

// ConvertValue converts a raw CSV string value to the appropriate internal type based on FeatureType.
//
// Args:
// - rawValue: Raw string value from CSV (may contain whitespace)
// - featureType: Target internal type (IntegerFeature/FloatFeature/StringFeature)
//
// Returns:
// - any: Converted value (int64, float64, or string), nil for missing values
//
// Security: Validates input and handles conversion errors by logging fatal errors
// Performance: O(1) string parsing operations
// Relationships: Used during CSV loading to convert raw strings to typed values
// Side effects: Calls logger.Fatal on conversion errors, terminating the application
//
// Examples:
//
//	ConvertValue("123", IntegerFeature) -> int64(123)
//	ConvertValue("45.67", FloatFeature) -> float64(45.67)
//	ConvertValue("sunny", StringFeature) -> "sunny"
//	ConvertValue("invalid", IntegerFeature) -> logs fatal error and exits
func ConvertValue(rawValue string, featureType FeatureType) any {
	// Handle empty/whitespace values as missing
	trimmedValue := strings.TrimSpace(rawValue)
	if trimmedValue == "" {
		logger.Error("missing value encountered during conversion")
		return nil // Missing values are represented as nil
	}

	switch featureType {
	case IntegerFeature:
		// Convert to int64 for integer features
		intVal, err := strconv.ParseInt(trimmedValue, 10, 64)
		if err != nil {
			logger.Fatal(fmt.Sprintf("cannot convert '%s' to integer: %v", rawValue, err))
		}
		return intVal

	case FloatFeature:
		// Convert to float64 for float features
		floatVal, err := strconv.ParseFloat(trimmedValue, 64)
		if err != nil {
			logger.Fatal(fmt.Sprintf("cannot convert '%s' to float: %v", rawValue, err))
		}
		return floatVal

	case StringFeature:
		return trimmedValue

	default:
		logger.Error(fmt.Sprintf("unsupported feature type: %v", featureType))
		return nil
	}
}
