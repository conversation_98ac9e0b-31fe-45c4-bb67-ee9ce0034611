// Package dataset provides functionality for loading and managing datasets
// from various file formats. It serves as a domain-specific layer that
// builds upon the raw format parsers to provide higher-level dataset operations.
package dataset

import (
	"fmt"
	"strings"

	"github.com/berrijam/mulberri/internal/data/features"
	"github.com/berrijam/mulberri/internal/io/formats/csv"
	"github.com/berrijam/mulberri/internal/utils/logger"
)

// Loader handles loading CSV data and basic type conversion.
// It provides a domain-specific interface for dataset loading operations
// while delegating the actual parsing to format-specific parsers.
//
// The Loader follows the separation of concerns principle where:
// - Format layer (io/formats/) handles raw parsing
// - Loader layer (data/*/loader.go) provides domain-specific loading
// - Future converter layer will handle type conversion
// - Future validation layer will handle business rules
type Loader struct {
	// csvParser handles the actual CSV file parsing operations
	csvParser *csv.Parser
}

// NewLoader creates a new dataset loader with default CSV parser settings.
// The returned loader is ready to use for loading CSV files.
//
// Returns:
//   - *Loader: A new loader instance configured with default settings
//
// Example:
//
//	loader := NewLoader()
//	data, err := loader.LoadCSV("data.csv")
func NewLoader() *Loader {
	return &Loader{
		csvParser: csv.NewParser(),
	}
}

// LoadCSV loads a CSV file and returns the parsed data as raw CSVData structure.
//
// Purpose: Provides low-level CSV file parsing with structured data extraction.
// Foundation method for higher-level dataset loading operations.
// Delegates actual parsing to format-specific CSV parser while providing domain interface.
//
// Args:
// - csvPath: File path to CSV data (must be readable file with proper CSV format)
//
// Returns:
// - *csv.CSVData: Structured CSV data with separated headers and records
//
// Constraints:
// - First row always treated as headers (column names)
// - All data rows must have consistent column count
// - File must be accessible and properly formatted CSV
//
// Security: No data validation - assumes pre-validated file paths
// Performance: O(n*m) where n=rows, m=columns for full file parsing
// Relationships: Delegates to csv.Parser, used by LoadCSVToDataset
// Side effects: File I/O operations, memory allocation for CSV data
//
// Returned CSVData Structure:
// - Headers: Column names from first row (used for feature identification)
// - Records: All data rows as string slices (raw values for conversion)
// - NumColumns: Column count (validated across all rows)
// - NumRows: Data row count (excludes header row)
//
// Memory Management:
// CSVData holds all string data in memory for processing flexibility.
// For large datasets, consider streaming or chunked processing.
// Call CSVData.Release() after conversion to typed Dataset to free memory.
// Avoid retaining both CSVData and converted Dataset simultaneously.
//
// Error Scenarios:
// - File not found or permission denied
// - Malformed CSV (inconsistent quotes, column counts)
// - Empty files or files with only headers
// - I/O errors during reading
//
// Usage Patterns:
//
//	// Basic CSV loading
//	loader := NewLoader()
//	csvData := loader.LoadCSV("data.csv")
//	if csvData == nil {
//	    // Handle loading error (logged by parser)
//	    return
//	}
//
//	// Process headers and records
//	fmt.Printf("Columns: %v\n", csvData.Headers)
//	fmt.Printf("Data: %d rows x %d columns\n", csvData.NumRows, csvData.NumColumns)
//
//	// Convert to typed dataset (recommended approach)
//	dataset, err := LoadCSVToDataset[string]("data.csv", featureTypes, "target")
//
//	// Clean up raw CSV data after conversion
//	csvData.Release()
//	csvData = nil
func (l *Loader) LoadCSV(csvPath string) *csv.CSVData {
	return l.csvParser.ParseCsvFile(csvPath)
}

// LoadCSVToDataset loads a CSV file and converts it directly to a typed Dataset with integrated type conversion.
//
// Purpose: Provides end-to-end CSV loading with automatic type conversion and dataset creation.
// Integrates raw CSV parsing with the ConvertValue function to eliminate manual conversion steps.
// Essential for machine learning workflows requiring typed column-based data structures.
// Supports both training and prediction modes with different validation and processing requirements.
//
// Args:
// - csvPath: File path to CSV data (must be valid, readable file with headers)
// - featureMetadata: Map of feature names to FeatureType for type conversion
// - targetColumn: Name of target column (excluded from features, used for ML labels)
// - operation: Processing mode that determines validation and column handling behavior:
//   - "train": Training mode - requires target column, processes all features, includes target values
//   - "predict": Prediction mode - target column optional, only processes features in metadata, excludes target values
//
// Returns:
//   - *Dataset[T]: Fully populated typed dataset with converted columns and targets (training mode)
//     or feature-only dataset (prediction mode)
//
// Constraints:
// - CSV must have headers in first row matching featureMetadata keys
// - Training mode: targetColumn must exist in CSV headers and all feature values must be convertible
// - Prediction mode: only features specified in featureMetadata are processed and validated
// - Generic type T must match target column data type (training mode only)
//
// Security: No sensitive data validation - assumes pre-validated CSV input
// Performance: O(n*m) where n=rows, m=columns for full data conversion
// Relationships: Uses Loader, ConvertValue, Dataset creation, and FeatureInfo metadata
// Side effects: Creates Dataset, logs conversion progress, releases CSV memory
//
// Processing Steps:
// 1. Load raw CSV using existing parser (validates format, extracts headers/records)
// 2. Validate target column exists (training mode only)
// 3. Create empty Dataset with appropriate capacity (pre-allocates memory)
// 4. Process feature columns based on operation mode:
//   - Training: Process all features with metadata, default missing ones to StringFeature
//   - Prediction: Only process features explicitly listed in featureMetadata
//
// 5. Convert and add target values (training mode only)
// 6. Set feature metadata for ML algorithm compatibility
//
// Operation Mode Behavior:
// Training Mode ("train" or invalid operation values):
// - Target column is required and must exist in CSV
// - All CSV columns are processed as features (excluding target)
// - Missing feature metadata defaults to StringFeature with warning
// - Target values are converted and added to dataset
// - Used for model training workflows
//
// Prediction Mode ("predict"):
// - Target column is optional and not processed even if present
// - Only features explicitly specified in featureMetadata are processed
// - CSV columns not in featureMetadata are skipped with debug log
// - No target values added to dataset
// - Used for model inference workflows
//
// Memory Management:
// Raw CSV data automatically released after conversion to prevent double memory usage.
// Dataset uses typed columns for memory efficiency compared to string storage.
//
// Error Handling:
// Comprehensive logging for debugging with file paths, column names, and row indices.
// Fails fast on type conversion errors to prevent corrupted datasets.
// Mode-specific validation ensures appropriate error messages.
//
// Example Usage:
//
//	// Training Mode - Load data for model training
//	featureTypes := map[string]features.FeatureType{
//	    "age": features.IntegerFeature,        // Convert "25" -> int64(25)
//	    "salary": features.FloatFeature,       // Convert "50000.5" -> float64(50000.5)
//	    "department": features.StringFeature,  // Keep "Engineering" -> "Engineering"
//	}
//
//	// Load training dataset with target column
//	trainDataset, err := LoadCSVToDataset[string]("train.csv", featureTypes, "performance_rating", "train")
//	if trainDataset == nil {
//	    log.Fatal()
//	}
//	// Dataset includes features + target values for training
//
//	// Prediction Mode - Load new data for inference
//	// Only specify features needed for prediction, target column ignored
//	predictionFeatures := map[string]features.FeatureType{
//	    "age": features.IntegerFeature,
//	    "salary": features.FloatFeature,
//	    "department": features.StringFeature,
//	}
//
//	// Load prediction dataset without target processing
//	predDataset, err := LoadCSVToDataset[string]("new_data.csv", predictionFeatures, "", "predict")
//	if predDataset == nil {
//	    log.Fatal()
//	// Dataset includes only specified features, no target values
//
//	// Dataset ready for ML algorithms
//	view := trainDataset.CreateView([]int{0, 1, 2}) // Create subset for training
func LoadCSVToDataset[T comparable](csvPath string, featureMetadata map[string]features.FeatureType, targetColumn string, operation string) *Dataset[T] {
	loader := NewLoader()
	// Step 1: Load raw CSV data
	csvData := loader.LoadCSV(csvPath)
	if csvData == nil {
		logger.Error(fmt.Sprintf("LoadCSVToDataset: failed to load CSV data from file '%s' - file may not exist, be corrupted, or have parsing errors", csvPath))
		return nil
	}
	defer csvData.Release() // Clean up raw CSV data after conversion

	// Step 2: Validate target column exists
	targetColumnIndex := -1
	for i, header := range csvData.Headers {
		if header == targetColumn {
			targetColumnIndex = i
			break
		}
	}
	// Validate target column based on operation
	if operation == "train" && targetColumnIndex == -1 {
		logger.Error(fmt.Sprintf("LoadCSVToDataset: target column '%s' not found in CSV headers. Available columns: %v", targetColumn, csvData.Headers))
		return nil
	}

	// Check for missing required features for prediction
	if operation == "predict" {
		missingFeatures := validateRequiredFeatures(csvData.Headers, featureMetadata, targetColumn)
		if len(missingFeatures) > 0 {
			logger.Fatal(fmt.Sprintf("Prediction dataset is missing %d required features from the model: %v. Available CSV columns: %v",
				len(missingFeatures), missingFeatures, csvData.Headers))
		}
	}

	// Step 3: Create dataset with appropriate capacity
	dataset := NewDataset[T](csvData.NumRows)

	// Step 4: Process each feature column (excluding target)
	for colIndex, featureName := range csvData.Headers {
		if colIndex == targetColumnIndex {
			continue // Skip target column
		}

		// Get feature type from metadata
		featureType, exists := featureMetadata[featureName]
		if !exists {
			if operation == "predict" {
				// In prediction mode, skip columns not specified in feature metadata
				logger.Warn(fmt.Sprintf("Column '%s' not found in feature metadata, skipping for prediction", featureName))
				continue
			} else {
				// In training mode, default to StringFeature for missing metadata
				logger.Warn(fmt.Sprintf("Feature type not specified for column '%s', defaulting to StringFeature", featureName))
				featureType = features.StringFeature
			}
		}

		// Convert column data and create appropriate column type
		convertAndAddColumn(dataset, csvData, colIndex, featureName, featureType)

		// Set feature metadata
		featureInfo := features.NewFeatureInfo(featureName, featureType, featureType.String())
		dataset.SetFeatureInfo(featureName, featureInfo)
	}

	// Step 5: Process target column for training mode or invalid operations
	if operation == "train" || (operation != "predict" && operation != "train") {
		convertAndAddTargets(dataset, csvData, targetColumnIndex)
	}

	logger.Info(fmt.Sprintf("Successfully loaded dataset: %d rows, %d features", dataset.totalSize, len(dataset.GetFeatureOrder())))
	return dataset
}

// convertAndAddColumn converts a CSV column to the appropriate typed column and adds it to the dataset.
//
// Purpose: Handles type-specific conversion from raw CSV strings to typed column data.
// Core component of the CSV loading pipeline that bridges string data to ML-ready types.
// Ensures data integrity through comprehensive null handling and type validation.
//
// Args:
// - dataset: Target Dataset to receive the converted column
// - csvData: Source CSV data containing raw string values
// - colIndex: Zero-based column index in CSV records
// - featureName: Name for the feature column (used for identification)
// - featureType: Target type for conversion (IntegerFeature/FloatFeature/StringFeature)
//
// Returns:
// - error: Detailed error if conversion or column addition fails
//
// Constraints:
// - colIndex must be valid within csvData.Records bounds
// - featureName must be unique within dataset
// - featureType must be supported (Integer/Float/String)
// - All non-null values must be convertible to target type
//
// Security: Uses ConvertValue which validates input and handles conversion errors
// Performance: O(n) where n=number of rows for single column conversion
// Relationships: Calls features.ConvertValue, Dataset.Add*Column methods
// Side effects: Adds typed column to dataset, logs conversion progress and warnings
//
// Type Conversion Process:
// 1. Creates appropriately sized data and nullMask slices
// 2. Iterates through all rows for the specified column
// 3. Converts each string value using features.ConvertValue
// 4. Handles null/missing values with nullMask pattern
// 5. Performs type assertion to extract typed value
// 6. Adds completed column to dataset with error handling
//
// Null Value Handling:
// Uses parallel nullMask boolean slice to track missing values.
// Null values (empty strings, conversion failures) marked as true in nullMask.
// Actual data slice contains zero values for null positions.
//
// Error Scenarios:
// - Unsupported feature type
// - Type assertion failures (shouldn't occur with proper ConvertValue)
// - Dataset column addition failures (duplicate names, etc.)
//
// Example Internal Flow:
//
//	// For IntegerFeature column "age" with values ["25", "", "30"]
//	data = [25, 0, 30]           // int64 slice
//	nullMask = [false, true, false]  // parallel boolean slice
//	// Result: age[0]=25 (valid), age[1]=null, age[2]=30 (valid)
func convertAndAddColumn[T comparable](dataset *Dataset[T], csvData *csv.CSVData, colIndex int, featureName string, featureType features.FeatureType) {
	numRows := csvData.NumRows
	logger.Info(fmt.Sprintf("convertAndAddColumn: converting column '%s' (index %d) with %d rows to type %s", featureName, colIndex, numRows, featureType.String()))

	switch featureType {
	case features.IntegerFeature:
		data := make([]int64, numRows)
		nullMask := make([]bool, numRows)

		nullCount := 0
		for rowIndex := 0; rowIndex < numRows; rowIndex++ {
			rawValue := csvData.Records[rowIndex][colIndex]
			convertedValue := features.ConvertValue(rawValue, featureType)

			if convertedValue == nil {
				nullMask[rowIndex] = true
				nullCount++
			} else {
				data[rowIndex] = convertedValue.(int64)
				nullMask[rowIndex] = false
			}
		}
		if nullCount > 0 {
			logger.Warn(fmt.Sprintf("convertAndAddColumn: column '%s' has %d null/missing values out of %d total rows", featureName, nullCount, numRows))
		}

		dataset.AddIntColumn(featureName, data, nullMask)

	case features.FloatFeature:
		data := make([]float64, numRows)
		nullMask := make([]bool, numRows)

		nullCount := 0
		for rowIndex := 0; rowIndex < numRows; rowIndex++ {
			rawValue := csvData.Records[rowIndex][colIndex]
			convertedValue := features.ConvertValue(rawValue, featureType)

			if convertedValue == nil {
				nullMask[rowIndex] = true
				nullCount++
			} else {
				data[rowIndex] = convertedValue.(float64)
				nullMask[rowIndex] = false
			}
		}
		if nullCount > 0 {
			logger.Warn(fmt.Sprintf("convertAndAddColumn: column '%s' has %d null/missing values out of %d total rows", featureName, nullCount, numRows))
		}

		dataset.AddFloatColumn(featureName, data, nullMask)

	case features.StringFeature:
		data := make([]string, numRows)
		nullMask := make([]bool, numRows)

		nullCount := 0
		for rowIndex := 0; rowIndex < numRows; rowIndex++ {
			rawValue := csvData.Records[rowIndex][colIndex]
			convertedValue := features.ConvertValue(rawValue, featureType)

			if convertedValue == nil {
				nullMask[rowIndex] = true
				nullCount++
			} else {
				data[rowIndex] = convertedValue.(string)
				nullMask[rowIndex] = false
			}
		}
		if nullCount > 0 {
			logger.Warn(fmt.Sprintf("convertAndAddColumn: column '%s' has %d null/missing values out of %d total rows", featureName, nullCount, numRows))
		}

		dataset.AddStringColumn(featureName, data, nullMask)

	default:
		logger.Error(fmt.Sprintf("convertAndAddColumn: unsupported feature type '%v' for column '%s'. Supported types: IntegerFeature, FloatFeature, StringFeature", featureType, featureName))
	}
}

// convertAndAddTargets converts the target column values and adds them to the dataset.
//
// Purpose: Handles conversion of target/label column from CSV strings to dataset target type.
// Essential for supervised learning where target values drive model training and evaluation.
// Provides type safety for target values while maintaining simplicity for common use cases.
//
// Args:
// - dataset: Target Dataset to receive converted target values
// - csvData: Source CSV data containing raw target strings
// - targetColumnIndex: Zero-based index of target column in CSV records
//
// Returns:
// - error: Detailed error if target conversion fails with row-specific information
//
// Constraints:
// - targetColumnIndex must be valid within csvData.Records bounds
// - All target values must be convertible to generic type T
// - Target type T must be comparable (required by Dataset generic constraint)
// - Currently optimized for string targets (most common ML use case)
//
// Security: Type-safe conversion with validation and detailed error reporting
// Performance: O(n) where n=number of rows for target column processing
// Relationships: Calls Dataset.AddTarget for each converted value
// Side effects: Populates dataset target array, logs conversion failures with context
//
// Type Conversion Strategy:
// Uses Go's type assertion to convert string values to target type T.
// This is a simplified approach that works well for string targets.
// For numeric targets (int, float), additional conversion logic would be needed.
//
// Current Implementation Notes:
// - Assumes T is string type for direct conversion
// - Uses any(rawValue).(T) type assertion for conversion
// - Fails fast on type conversion errors to prevent corrupted datasets
// - Future enhancement: Add numeric target type support
//
// Error Handling:
// Provides detailed context including row index, raw value, and expected type.
// Logs conversion failures before returning error for debugging.
// Preserves data integrity by failing on first conversion error.
//
// Example Target Conversions:
//
//	// For string targets (current implementation)
//	"ClassA" -> T("ClassA")  // Direct string conversion
//	"positive" -> T("positive")
//
//	// Future numeric target support would add:
//	"1" -> T(1)      // For integer targets
//	"0.85" -> T(0.85) // For float targets
//
// Usage Context:
// Called after all feature columns are processed to complete dataset creation.
// Target values used by ML algorithms for training supervision and evaluation metrics.
func convertAndAddTargets[T comparable](dataset *Dataset[T], csvData *csv.CSVData, targetColumnIndex int) {
	for rowIndex := 0; rowIndex < csvData.NumRows; rowIndex++ {
		rawTargetValue := csvData.Records[rowIndex][targetColumnIndex]

		// Simple conversion - assumes T is string type
		// For production use, you'd want more sophisticated type conversion
		trimmedValue := strings.TrimSpace(rawTargetValue)
		targetValue, ok := any(trimmedValue).(T)
		if !ok {
			logger.Error(fmt.Sprintf("convertAndAddTargets: cannot convert target value '%s' (row %d) to target type %T. Raw value type: %T", rawTargetValue, rowIndex, targetValue, rawTargetValue))
			return
		}

		dataset.AddTarget(targetValue)
	}
}

// validateRequiredFeatures checks if all required model features are present in the CSV headers.
//
// This function ensures that the prediction CSV contains all features that the model expects.
// Missing features would prevent accurate predictions, so this validation is critical.
//
// Args:
//   - csvHeaders: Column names from the CSV file
//   - featureMetadata: Map of required feature names to their types from the model
//
// Returns:
//   - []string: List of missing feature names (empty if all features are present)
//
// Performance: O(n*m) where n=number of features, m=number of CSV headers
// Side effects: None, pure function
func validateRequiredFeatures(csvHeaders []string, featureMetadata map[string]features.FeatureType, targetColumn string) []string {
	// Create a set of available CSV headers for efficient lookup
	csvHeaderSet := make(map[string]bool)
	for _, header := range csvHeaders {
		csvHeaderSet[header] = true
	}

	// Check which required features are missing
	var missingFeatures []string
	for featureName := range featureMetadata {

		if !csvHeaderSet[featureName] {
			if featureName == targetColumn {
				continue
			}
			missingFeatures = append(missingFeatures, featureName)
		}
	}

	return missingFeatures
}
