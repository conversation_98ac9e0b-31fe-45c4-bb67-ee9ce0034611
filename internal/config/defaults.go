// Package config provides default configuration values for Mulberri decision tree toolkit.
//
// Centralizes core algorithm parameters and basic settings to ensure consistency
// across training and prediction operations.
package config

const (
	// DefaultMaxDepthAlpha is the fractional multiplier for calculating max depth.
	// max_depth = alpha * log2(number_of_features)
	// 0.8 provides a more conservative depth limit for better generalization.
	DefaultMaxDepthAlpha = 0.8

	// DefaultMinSamplesSplitPercent sets percentage of dataset for minimum split samples.
	// 1% means for 1000 samples, minimum 10 samples required to split.
	// Allows grid-search optimization while preventing overfitting.
	DefaultMinSamplesSplitPercent = 0.01

	// DefaultMinSamplesLeafPercent sets percentage of dataset for minimum leaf samples.
	// 0.5% means for 1000 samples, minimum 5 samples required in leaf.
	// Allows grid-search optimization while preventing overfitting.
	DefaultMinSamplesLeafPercent = 0.005

	// DefaultMinSplitImprovement sets minimum gain ratio improvement required for splits.
	// 0.01 means split must improve gain ratio by at least 1% to be accepted.
	// Prevents splits with minimal information gain.
	DefaultMinSplitImprovement = 0.01

	// DefaultCriterion specifies impurity measure for the algorithm.
	DefaultCriterion = "entropy"
)

// Supported types from YAML feature config (project specification)
var SupportedFeatureTypes = []string{
	"nominal", "numeric", "date", "datetime", "time", "binary",
}

// Supported handle_as types for internal processing (project specification)
var SupportedHandleAsTypes = []string{
	"integer", "float", "string",
}

// Application metadata
const (
	Version   = "0.1.0"
	Algorithm = "C4.5"
)
