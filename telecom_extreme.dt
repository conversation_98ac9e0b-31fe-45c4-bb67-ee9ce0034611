{"features": [{"name": "age", "type": "float"}, {"name": "avg_monthly_gb_download", "type": "float"}, {"name": "avg_monthly_long_distance_charges", "type": "float"}, {"name": "city", "type": "string"}, {"name": "contract", "type": "string"}, {"name": "device_protection_plan", "type": "string"}, {"name": "gender", "type": "string"}, {"name": "internet_service", "type": "string"}, {"name": "internet_type", "type": "string"}, {"name": "latitude", "type": "float"}, {"name": "longitude", "type": "float"}, {"name": "married", "type": "string"}, {"name": "monthly_charge", "type": "float"}, {"name": "multiple_lines", "type": "string"}, {"name": "number_of_dependents", "type": "float"}, {"name": "number_of_referrals", "type": "float"}, {"name": "offer", "type": "string"}, {"name": "online_backup", "type": "string"}, {"name": "online_security", "type": "string"}, {"name": "paperless_billing", "type": "string"}, {"name": "payment_method", "type": "string"}, {"name": "phone_service", "type": "string"}, {"name": "population", "type": "float"}, {"name": "premium_tech_support", "type": "string"}, {"name": "streaming_movies", "type": "string"}, {"name": "streaming_music", "type": "string"}, {"name": "streaming_tv", "type": "string"}, {"name": "tenure_in_months", "type": "float"}, {"name": "total_charges", "type": "float"}, {"name": "total_extra_data_charges", "type": "float"}, {"name": "total_long_distance_charges", "type": "float"}, {"name": "total_refunds", "type": "float"}, {"name": "total_revenue", "type": "float"}, {"name": "unlimited_data", "type": "string"}, {"name": "zip_code", "type": "float"}], "classes": ["No", "Yes"], "metadata": {"created_at": "2025-10-01T19:50:50.021414691+03:00", "algorithm": "C4.5", "max_depth": 15, "min_samples": 2, "criterion": "entropy", "total_nodes": 450, "leaf_nodes": 354, "training_samples": 5634, "target_column": "churn"}, "root": {"type": "decision", "feature": {"name": "zip_code", "type": "float"}, "split_value": 96149, "children": {"gt": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 2}, "samples": 2, "confidence": 1}, "lte": {"type": "decision", "feature": {"name": "total_charges", "type": "float"}, "split_value": 8678.625, "children": {"gt": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "lte": {"type": "decision", "feature": {"name": "contract", "type": "string"}, "split_value": "contract", "children": {"Month-to-Month": {"type": "decision", "feature": {"name": "number_of_referrals", "type": "float"}, "split_value": 7.5, "children": {"gt": {"type": "decision", "feature": {"name": "total_extra_data_charges", "type": "float"}, "split_value": 135, "children": {"gt": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "lte": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 143, "Yes": 1}, "samples": 144, "confidence": 0.9930555555555556}}, "class_distribution": {"No": 143, "Yes": 2}, "samples": 145, "confidence": 0.9862068965517241}, "lte": {"type": "decision", "feature": {"name": "age", "type": "float"}, "split_value": 64.5, "children": {"gt": {"type": "decision", "feature": {"name": "monthly_charge", "type": "float"}, "split_value": 114.85, "children": {"gt": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 1}, "samples": 1, "confidence": 1}, "lte": {"type": "decision", "feature": {"name": "number_of_referrals", "type": "float"}, "split_value": 6.5, "children": {"gt": {"type": "decision", "feature": {"name": "total_long_distance_charges", "type": "float"}, "split_value": 252.68, "children": {"gt": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 4}, "samples": 4, "confidence": 1}, "lte": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}}, "class_distribution": {"No": 4, "Yes": 1}, "samples": 5, "confidence": 0.8}, "lte": {"type": "decision", "feature": {"name": "latitude", "type": "float"}, "split_value": 32.7231355, "children": {"gt": {"type": "decision", "feature": {"name": "tenure_in_months", "type": "float"}, "split_value": 58.5, "children": {"gt": {"type": "decision", "feature": {"name": "total_revenue", "type": "float"}, "split_value": 7967.975, "children": {"gt": {"type": "decision", "feature": {"name": "monthly_charge", "type": "float"}, "split_value": 107.35, "children": {"gt": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 1}, "samples": 1, "confidence": 1}, "lte": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 7}, "samples": 7, "confidence": 1}}, "class_distribution": {"No": 1, "Yes": 7}, "samples": 8, "confidence": 0.875}, "lte": {"type": "decision", "feature": {"name": "total_refunds", "type": "float"}, "split_value": 3.395, "children": {"gt": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 2}, "samples": 2, "confidence": 1}, "lte": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 7}, "samples": 7, "confidence": 1}}, "class_distribution": {"No": 7, "Yes": 2}, "samples": 9, "confidence": 0.7777777777777778}}, "class_distribution": {"No": 8, "Yes": 9}, "samples": 17, "confidence": 0.5294117647058824}, "lte": {"type": "decision", "feature": {"name": "monthly_charge", "type": "float"}, "split_value": -2.5, "children": {"gt": {"type": "decision", "feature": {"name": "number_of_referrals", "type": "float"}, "split_value": 3.5, "children": {"gt": {"type": "decision", "feature": {"name": "total_revenue", "type": "float"}, "split_value": 3676.1899999999996, "children": {"gt": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 4}, "samples": 4, "confidence": 1}, "lte": {"type": "decision", "feature": {"name": "zip_code", "type": "float"}, "split_value": 90822.5, "children": {"gt": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 7}, "samples": 7, "confidence": 1}, "lte": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 2}, "samples": 2, "confidence": 1}}, "class_distribution": {"No": 2, "Yes": 7}, "samples": 9, "confidence": 0.7777777777777778}}, "class_distribution": {"No": 6, "Yes": 7}, "samples": 13, "confidence": 0.5384615384615384}, "lte": {"type": "decision", "feature": {"name": "total_charges", "type": "float"}, "split_value": 5067.825000000001, "children": {"gt": {"type": "decision", "feature": {"name": "total_charges", "type": "float"}, "split_value": 5462.225, "children": {"gt": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 4}, "samples": 4, "confidence": 1}, "lte": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 3}, "samples": 3, "confidence": 1}}, "class_distribution": {"No": 3, "Yes": 4}, "samples": 7, "confidence": 0.5714285714285714}, "lte": {"type": "decision", "feature": {"name": "city", "type": "string"}, "split_value": "city", "children": {"Acton": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 1}, "samples": 1, "confidence": 1}, "Adin": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Alderpoint": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Alhambra": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Alpine": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Anaheim": {"type": "decision", "feature": {"name": "avg_monthly_gb_download", "type": "float"}, "split_value": 20, "children": {"gt": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "lte": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 1}, "samples": 1, "confidence": 1}}, "class_distribution": {"No": 1, "Yes": 1}, "samples": 2, "confidence": 0.5}, "Anderson": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Angels Camp": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Applegate": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Aromas": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 1}, "samples": 1, "confidence": 1}, "Atherton": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Auburn": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Avenal": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Ballico": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Bayside": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Bellflower": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Berkeley": {"type": "decision", "feature": {"name": "total_charges", "type": "float"}, "split_value": 823.55, "children": {"gt": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 1}, "samples": 1, "confidence": 1}, "lte": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 2}, "samples": 2, "confidence": 1}}, "class_distribution": {"No": 1, "Yes": 2}, "samples": 3, "confidence": 0.6666666666666666}, "Big Oak Flat": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 1}, "samples": 1, "confidence": 1}, "Birds Landing": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Blue Lake": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Bonsall": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Boron": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Boulder Creek": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 2}, "samples": 2, "confidence": 1}, "Bradley": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Branscomb": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Brea": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Bridgeville": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Brownsville": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Buena Park": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 3}, "samples": 3, "confidence": 1}, "Burbank": {"type": "decision", "feature": {"name": "avg_monthly_long_distance_charges", "type": "float"}, "split_value": 32, "children": {"gt": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 2}, "samples": 2, "confidence": 1}, "lte": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 2}, "samples": 2, "confidence": 1}}, "class_distribution": {"No": 2, "Yes": 2}, "samples": 4, "confidence": 0.5}, "Butte City": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Byron": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Camarillo": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Canoga Park": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Canyon Country": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Canyon Dam": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Castaic": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 2}, "samples": 2, "confidence": 1}, "Catheys Valley": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Chino": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Chualar": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Citrus Heights": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Claremont": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Clearlake": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Clipper Mills": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Clovis": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Coalinga": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Cobb": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Columbia": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Concord": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 2}, "samples": 2, "confidence": 1}, "Corona Del Mar": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Corte Madera": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Covina": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 1}, "samples": 1, "confidence": 1}, "Crestline": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Crockett": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Crows Landing": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Culver City": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Cutler": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Daly City": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Davenport": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Davis": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Davis Creek": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 1}, "samples": 1, "confidence": 1}, "Delano": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Desert Center": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Desert Hot Springs": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Dinuba": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Dobbins": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Downey": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 2}, "samples": 2, "confidence": 1}, "Doyle": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Ducor": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Dunlap": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "El Centro": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "El Monte": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 2}, "samples": 2, "confidence": 1}, "El Portal": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 1}, "samples": 1, "confidence": 1}, "Elk Creek": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Emeryville": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Encino": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 1}, "samples": 1, "confidence": 1}, "Escondido": {"type": "decision", "feature": {"name": "total_extra_data_charges", "type": "float"}, "split_value": 10, "children": {"gt": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 1}, "samples": 1, "confidence": 1}, "lte": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 7}, "samples": 7, "confidence": 1}}, "class_distribution": {"No": 1, "Yes": 7}, "samples": 8, "confidence": 0.875}, "Fair Oaks": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Fallbrook": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 3}, "samples": 3, "confidence": 1}, "Forest Knolls": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Forest Ranch": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Fowler": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Freedom": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Fremont": {"type": "decision", "feature": {"name": "payment_method", "type": "string"}, "split_value": "payment_method", "children": {"Bank Withdrawal": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 1}, "samples": 1, "confidence": 1}, "Credit Card": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}}, "class_distribution": {"No": 1, "Yes": 1}, "samples": 2, "confidence": 0.5}, "Fresno": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 1}, "samples": 1, "confidence": 1}, "Fullerton": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 2}, "samples": 2, "confidence": 1}, "Fulton": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Garberville": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Gardena": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Gasquet": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Glencoe": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Glendale": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 1}, "samples": 1, "confidence": 1}, "Glendora": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Granite Bay": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 1}, "samples": 1, "confidence": 1}, "Grass Valley": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Greenbrae": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Greenville": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 1}, "samples": 1, "confidence": 1}, "Grimes": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Grizzly Flats": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Guinda": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Half Moon Bay": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Hayward": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Helm": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Hemet": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Herald": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Hermosa Beach": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Hornbrook": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Huntington Beach": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Huron": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Indian Wells": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 2}, "samples": 2, "confidence": 1}, "Inglewood": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Irvine": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 2}, "samples": 2, "confidence": 1}, "Johannesburg": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Jolon": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "La Canada Flintridge": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "La Mesa": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 1}, "samples": 1, "confidence": 1}, "La Puente": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Lake Elsinore": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Lake Forest": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Lakewood": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Le Grand": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 1}, "samples": 1, "confidence": 1}, "Lee Vining": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Lewiston": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 2}, "samples": 2, "confidence": 1}, "Live Oak": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 1}, "samples": 1, "confidence": 1}, "Livermore": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Loleta": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Long Beach": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 2}, "samples": 2, "confidence": 1}, "Los Alamitos": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Los Alamos": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Los Angeles": {"type": "decision", "feature": {"name": "longitude", "type": "float"}, "split_value": -118.3270055, "children": {"gt": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 10}, "samples": 10, "confidence": 1}, "lte": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 4}, "samples": 4, "confidence": 1}}, "class_distribution": {"No": 4, "Yes": 10}, "samples": 14, "confidence": 0.7142857142857143}, "Los Gatos": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Lucerne Valley": {"type": "decision", "feature": {"name": "multiple_lines", "type": "string"}, "split_value": "multiple_lines", "children": {"No": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Yes": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 1}, "samples": 1, "confidence": 1}}, "class_distribution": {"No": 1, "Yes": 1}, "samples": 2, "confidence": 0.5}, "Madison": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Malibu": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Marina": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Martinez": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Mather": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Mendocino": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Merced": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 1}, "samples": 1, "confidence": 1}, "Mineral": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Modesto": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Montebello": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Monterey": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 1}, "samples": 1, "confidence": 1}, "Montrose": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Moorpark": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Moraga": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Moreno Valley": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Morgan Hill": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Moss Landing": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Mount Laguna": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Murphys": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Murrieta": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Napa": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Nevada City": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Newcastle": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Nice": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Nipomo": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "North Hollywood": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 3}, "samples": 3, "confidence": 1}, "Novato": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Nuevo": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Oak View": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Oakland": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 2}, "samples": 2, "confidence": 1}, "Oakley": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Ontario": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Pacific Palisades": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Pacoima": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Pala": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Palmdale": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Palo Alto": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Palo Cedro": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Paramount": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Parlier": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Pasadena": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Paskenta": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 3}, "samples": 3, "confidence": 1}, "Pauma Valley": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Perris": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 2}, "samples": 2, "confidence": 1}, "Philo": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Pico Rivera": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Pioneer": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Pleasant Grove": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Pleasanton": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 3}, "samples": 3, "confidence": 1}, "Point Arena": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Port Costa": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Princeton": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Rancho Cordova": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Red Bluff": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Redding": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Redlands": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Redwood City": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Reseda": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Rio Dell": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Riverside": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Rosemead": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Round Mountain": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Rowland Heights": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Sacramento": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 4}, "samples": 4, "confidence": 1}, "San Bernardino": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "San Diego": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 33}, "samples": 33, "confidence": 1}, "San Francisco": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 7}, "samples": 7, "confidence": 1}, "San Gabriel": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "San Jacinto": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "San Jose": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 5}, "samples": 5, "confidence": 1}, "San Lucas": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "San Luis Obispo": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "San Mateo": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "San Pablo": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "San Pedro": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "San Rafael": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 2}, "samples": 2, "confidence": 1}, "San Ramon": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Santa Barbara": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Santa Cruz": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 1}, "samples": 1, "confidence": 1}, "Santa Rosa": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Saratoga": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 1}, "samples": 1, "confidence": 1}, "Selma": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 2}, "samples": 2, "confidence": 1}, "Shaver Lake": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Sheep Ranch": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Sheridan": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Sherman Oaks": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Sierra City": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Simi Valley": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Sloughhouse": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Soda Springs": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Somis": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "South Pasadena": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Spring Valley": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Squaw Valley": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Stevenson Ranch": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Stinson Beach": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Stockton": {"type": "decision", "feature": {"name": "tenure_in_months", "type": "float"}, "split_value": 2, "children": {"gt": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 4}, "samples": 4, "confidence": 1}, "lte": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 1}, "samples": 1, "confidence": 1}}, "class_distribution": {"No": 1, "Yes": 4}, "samples": 5, "confidence": 0.8}, "Studio City": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 1}, "samples": 1, "confidence": 1}, "Summerland": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Sutter": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Sylmar": {"type": "decision", "feature": {"name": "monthly_charge", "type": "float"}, "split_value": 71.425, "children": {"gt": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 1}, "samples": 1, "confidence": 1}, "lte": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}}, "class_distribution": {"No": 1, "Yes": 1}, "samples": 2, "confidence": 0.5}, "Tehachapi": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 1}, "samples": 1, "confidence": 1}, "Temecula": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 4}, "samples": 4, "confidence": 1}, "The Sea Ranch": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Thermal": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Thousand Oaks": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Thousand Palms": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 1}, "samples": 1, "confidence": 1}, "Tranquillity": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Trinity Center": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Twain": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Vallejo": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Van Nuys": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 2}, "samples": 2, "confidence": 1}, "Victorville": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Vista": {"type": "decision", "feature": {"name": "offer", "type": "string"}, "split_value": "offer", "children": {"No Offer": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 1}, "samples": 1, "confidence": 1}, "Offer E": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}}, "class_distribution": {"No": 1, "Yes": 1}, "samples": 2, "confidence": 0.5}, "Waterford": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 1}, "samples": 1, "confidence": 1}, "Weaverville": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Westport": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Wheatland": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Whittier": {"type": "decision", "feature": {"name": "offer", "type": "string"}, "split_value": "offer", "children": {"No Offer": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 1}, "samples": 1, "confidence": 1}, "Offer C": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}}, "class_distribution": {"No": 1, "Yes": 1}, "samples": 2, "confidence": 0.5}, "Williams": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Winton": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Woodbridge": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Woody": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Wrightwood": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 2}, "samples": 2, "confidence": 1}, "Yuba City": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Yucaipa": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}}, "class_distribution": {"No": 39, "Yes": 328}, "samples": 367, "confidence": 0.8937329700272479}}, "class_distribution": {"No": 42, "Yes": 332}, "samples": 374, "confidence": 0.8877005347593583}}, "class_distribution": {"No": 48, "Yes": 339}, "samples": 387, "confidence": 0.875968992248062}, "lte": {"type": "decision", "feature": {"name": "married", "type": "string"}, "split_value": "married", "children": {"No": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 3}, "samples": 3, "confidence": 1}, "Yes": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 3}, "samples": 3, "confidence": 1}}, "class_distribution": {"No": 3, "Yes": 3}, "samples": 6, "confidence": 0.5}}, "class_distribution": {"No": 51, "Yes": 342}, "samples": 393, "confidence": 0.8702290076335878}}, "class_distribution": {"No": 59, "Yes": 351}, "samples": 410, "confidence": 0.8560975609756097}, "lte": {"type": "decision", "feature": {"name": "avg_monthly_long_distance_charges", "type": "float"}, "split_value": 33.235, "children": {"gt": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "lte": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 2}, "samples": 2, "confidence": 1}}, "class_distribution": {"No": 2, "Yes": 1}, "samples": 3, "confidence": 0.6666666666666666}}, "class_distribution": {"No": 61, "Yes": 352}, "samples": 413, "confidence": 0.8523002421307506}}, "class_distribution": {"No": 65, "Yes": 353}, "samples": 418, "confidence": 0.8444976076555024}}, "class_distribution": {"No": 66, "Yes": 353}, "samples": 419, "confidence": 0.8424821002386634}, "lte": {"type": "decision", "feature": {"name": "monthly_charge", "type": "float"}, "split_value": 112.225, "children": {"gt": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 3}, "samples": 3, "confidence": 1}, "lte": {"type": "decision", "feature": {"name": "total_revenue", "type": "float"}, "split_value": 10374.224999999999, "children": {"gt": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "lte": {"type": "decision", "feature": {"name": "longitude", "type": "float"}, "split_value": -115.224433, "children": {"gt": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 12}, "samples": 12, "confidence": 1}, "lte": {"type": "decision", "feature": {"name": "total_charges", "type": "float"}, "split_value": 7383.9, "children": {"gt": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "lte": {"type": "decision", "feature": {"name": "total_extra_data_charges", "type": "float"}, "split_value": 145, "children": {"gt": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 7}, "samples": 7, "confidence": 1}, "lte": {"type": "decision", "feature": {"name": "number_of_referrals", "type": "float"}, "split_value": 5.5, "children": {"gt": {"type": "decision", "feature": {"name": "total_long_distance_charges", "type": "float"}, "split_value": 2848.2349999999997, "children": {"gt": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "lte": {"type": "decision", "feature": {"name": "total_refunds", "type": "float"}, "split_value": 2.24, "children": {"gt": {"type": "decision", "feature": {"name": "total_refunds", "type": "float"}, "split_value": 30.095000000000002, "children": {"gt": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 6}, "samples": 6, "confidence": 1}, "lte": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 3}, "samples": 3, "confidence": 1}}, "class_distribution": {"No": 6, "Yes": 3}, "samples": 9, "confidence": 0.6666666666666666}, "lte": {"type": "decision", "feature": {"name": "population", "type": "float"}, "split_value": 59897, "children": {"gt": {"type": "decision", "feature": {"name": "tenure_in_months", "type": "float"}, "split_value": 21, "children": {"gt": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 4}, "samples": 4, "confidence": 1}, "lte": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}}, "class_distribution": {"No": 4, "Yes": 1}, "samples": 5, "confidence": 0.8}, "lte": {"type": "decision", "feature": {"name": "latitude", "type": "float"}, "split_value": 32.9460865, "children": {"gt": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 73, "Yes": 1}, "samples": 74, "confidence": 0.9864864864864865}, "lte": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 3, "Yes": 1}, "samples": 4, "confidence": 0.75}}, "class_distribution": {"No": 76, "Yes": 2}, "samples": 78, "confidence": 0.9743589743589743}}, "class_distribution": {"No": 80, "Yes": 3}, "samples": 83, "confidence": 0.963855421686747}}, "class_distribution": {"No": 86, "Yes": 6}, "samples": 92, "confidence": 0.9347826086956522}}, "class_distribution": {"No": 86, "Yes": 7}, "samples": 93, "confidence": 0.9247311827956989}, "lte": {"type": "decision", "feature": {"name": "total_charges", "type": "float"}, "split_value": 6947.299999999999, "children": {"gt": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 4}, "samples": 4, "confidence": 1}, "lte": {"type": "decision", "feature": {"name": "total_charges", "type": "float"}, "split_value": 6928.075, "children": {"gt": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "lte": {"type": "decision", "feature": {"name": "total_long_distance_charges", "type": "float"}, "split_value": 3016.3, "children": {"gt": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 4}, "samples": 4, "confidence": 1}, "lte": {"type": "decision", "feature": {"name": "total_long_distance_charges", "type": "float"}, "split_value": 2928.185, "children": {"gt": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 2}, "samples": 2, "confidence": 1}, "lte": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 1234, "Yes": 955}, "samples": 2189, "confidence": 0.5637277295568753}}, "class_distribution": {"No": 1234, "Yes": 957}, "samples": 2191, "confidence": 0.5632131446827933}}, "class_distribution": {"No": 1238, "Yes": 957}, "samples": 2195, "confidence": 0.564009111617312}}, "class_distribution": {"No": 1238, "Yes": 958}, "samples": 2196, "confidence": 0.563752276867031}}, "class_distribution": {"No": 1242, "Yes": 958}, "samples": 2200, "confidence": 0.5645454545454546}}, "class_distribution": {"No": 1328, "Yes": 965}, "samples": 2293, "confidence": 0.5791539467945922}}, "class_distribution": {"No": 1335, "Yes": 965}, "samples": 2300, "confidence": 0.5804347826086956}}, "class_distribution": {"No": 1335, "Yes": 966}, "samples": 2301, "confidence": 0.5801825293350718}}, "class_distribution": {"No": 1347, "Yes": 966}, "samples": 2313, "confidence": 0.582360570687419}}, "class_distribution": {"No": 1347, "Yes": 967}, "samples": 2314, "confidence": 0.5821089023336214}}, "class_distribution": {"No": 1347, "Yes": 970}, "samples": 2317, "confidence": 0.5813552006905481}}, "class_distribution": {"No": 1413, "Yes": 1323}, "samples": 2736, "confidence": 0.5164473684210527}}, "class_distribution": {"No": 1556, "Yes": 1325}, "samples": 2881, "confidence": 0.5400902464422076}, "One Year": {"type": "decision", "feature": {"name": "longitude", "type": "float"}, "split_value": -124.2707115, "children": {"gt": {"type": "decision", "feature": {"name": "total_refunds", "type": "float"}, "split_value": 49.4, "children": {"gt": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "lte": {"type": "decision", "feature": {"name": "zip_code", "type": "float"}, "split_value": 96146.5, "children": {"gt": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "lte": {"type": "decision", "feature": {"name": "total_charges", "type": "float"}, "split_value": 8072.875, "children": {"gt": {"type": "decision", "feature": {"name": "age", "type": "float"}, "split_value": 26, "children": {"gt": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 2}, "samples": 2, "confidence": 1}, "lte": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 1}, "samples": 1, "confidence": 1}}, "class_distribution": {"No": 1, "Yes": 2}, "samples": 3, "confidence": 0.6666666666666666}, "lte": {"type": "decision", "feature": {"name": "total_refunds", "type": "float"}, "split_value": 48.42, "children": {"gt": {"type": "decision", "feature": {"name": "phone_service", "type": "string"}, "split_value": "phone_service", "children": {"No": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 1}, "samples": 1, "confidence": 1}, "Yes": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}}, "class_distribution": {"No": 1, "Yes": 1}, "samples": 2, "confidence": 0.5}, "lte": {"type": "decision", "feature": {"name": "monthly_charge", "type": "float"}, "split_value": 117.775, "children": {"gt": {"type": "decision", "feature": {"name": "avg_monthly_gb_download", "type": "float"}, "split_value": 34.5, "children": {"gt": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 1}, "samples": 1, "confidence": 1}, "lte": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}}, "class_distribution": {"No": 1, "Yes": 1}, "samples": 2, "confidence": 0.5}, "lte": {"type": "decision", "feature": {"name": "population", "type": "float"}, "split_value": 20, "children": {"gt": {"type": "decision", "feature": {"name": "number_of_dependents", "type": "float"}, "split_value": 0.5, "children": {"gt": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 298}, "samples": 298, "confidence": 1}, "lte": {"type": "decision", "feature": {"name": "number_of_referrals", "type": "float"}, "split_value": 5.5, "children": {"gt": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 164, "Yes": 1}, "samples": 165, "confidence": 0.9939393939393939}, "lte": {"type": "decision", "feature": {"name": "monthly_charge", "type": "float"}, "split_value": 99.17500000000001, "children": {"gt": {"type": "decision", "feature": {"name": "total_revenue", "type": "float"}, "split_value": 2355.735, "children": {"gt": {"type": "decision", "feature": {"name": "internet_type", "type": "string"}, "split_value": "internet_type", "children": {"Cable": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 3}, "samples": 3, "confidence": 1}, "Fiber Optic": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 90, "Yes": 43}, "samples": 133, "confidence": 0.6766917293233082}}, "class_distribution": {"No": 90, "Yes": 46}, "samples": 136, "confidence": 0.6617647058823529}, "lte": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 3}, "samples": 3, "confidence": 1}}, "class_distribution": {"No": 90, "Yes": 49}, "samples": 139, "confidence": 0.6474820143884892}, "lte": {"type": "decision", "feature": {"name": "total_revenue", "type": "float"}, "split_value": 8655.535, "children": {"gt": {"type": "decision", "feature": {"name": "total_charges", "type": "float"}, "split_value": 6256.9, "children": {"gt": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 2}, "samples": 2, "confidence": 1}, "lte": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 2}, "samples": 2, "confidence": 1}}, "class_distribution": {"No": 2, "Yes": 2}, "samples": 4, "confidence": 0.5}, "lte": {"type": "decision", "feature": {"name": "avg_monthly_gb_download", "type": "float"}, "split_value": 83.5, "children": {"gt": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 2, "Yes": 2}, "samples": 4, "confidence": 0.5}, "lte": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 551, "Yes": 66}, "samples": 617, "confidence": 0.893030794165316}}, "class_distribution": {"No": 553, "Yes": 68}, "samples": 621, "confidence": 0.8904991948470209}}, "class_distribution": {"No": 555, "Yes": 70}, "samples": 625, "confidence": 0.888}}, "class_distribution": {"No": 645, "Yes": 119}, "samples": 764, "confidence": 0.8442408376963351}}, "class_distribution": {"No": 809, "Yes": 120}, "samples": 929, "confidence": 0.8708288482238966}}, "class_distribution": {"No": 1107, "Yes": 120}, "samples": 1227, "confidence": 0.902200488997555}, "lte": {"type": "decision", "feature": {"name": "phone_service", "type": "string"}, "split_value": "phone_service", "children": {"No": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 1}, "samples": 1, "confidence": 1}, "Yes": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}}, "class_distribution": {"No": 1, "Yes": 1}, "samples": 2, "confidence": 0.5}}, "class_distribution": {"No": 1108, "Yes": 121}, "samples": 1229, "confidence": 0.9015459723352319}}, "class_distribution": {"No": 1109, "Yes": 122}, "samples": 1231, "confidence": 0.90089358245329}}, "class_distribution": {"No": 1110, "Yes": 123}, "samples": 1233, "confidence": 0.9002433090024331}}, "class_distribution": {"No": 1111, "Yes": 125}, "samples": 1236, "confidence": 0.8988673139158576}}, "class_distribution": {"No": 1111, "Yes": 126}, "samples": 1237, "confidence": 0.8981406628940987}}, "class_distribution": {"No": 1111, "Yes": 127}, "samples": 1238, "confidence": 0.8974151857835219}, "lte": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}}, "class_distribution": {"No": 1111, "Yes": 128}, "samples": 1239, "confidence": 0.8966908797417272}, "Two Year": {"type": "decision", "feature": {"name": "latitude", "type": "float"}, "split_value": 41.941445, "children": {"gt": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "lte": {"type": "decision", "feature": {"name": "total_long_distance_charges", "type": "float"}, "split_value": 3501.13, "children": {"gt": {"type": "decision", "feature": {"name": "age", "type": "float"}, "split_value": 34, "children": {"gt": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 3}, "samples": 3, "confidence": 1}, "lte": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}}, "class_distribution": {"No": 3, "Yes": 1}, "samples": 4, "confidence": 0.75}, "lte": {"type": "decision", "feature": {"name": "longitude", "type": "float"}, "split_value": -115.344671, "children": {"gt": {"type": "decision", "feature": {"name": "avg_monthly_gb_download", "type": "float"}, "split_value": 27, "children": {"gt": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "lte": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 6}, "samples": 6, "confidence": 1}}, "class_distribution": {"No": 6, "Yes": 1}, "samples": 7, "confidence": 0.8571428571428571}, "lte": {"type": "decision", "feature": {"name": "total_refunds", "type": "float"}, "split_value": 48.135, "children": {"gt": {"type": "decision", "feature": {"name": "total_charges", "type": "float"}, "split_value": 6317.625, "children": {"gt": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "lte": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 6}, "samples": 6, "confidence": 1}}, "class_distribution": {"No": 6, "Yes": 1}, "samples": 7, "confidence": 0.8571428571428571}, "lte": {"type": "decision", "feature": {"name": "number_of_dependents", "type": "float"}, "split_value": 0.5, "children": {"gt": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 508}, "samples": 508, "confidence": 1}, "lte": {"type": "decision", "feature": {"name": "total_extra_data_charges", "type": "float"}, "split_value": 145, "children": {"gt": {"type": "decision", "feature": {"name": "streaming_movies", "type": "string"}, "split_value": "streaming_movies", "children": {"No": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Yes": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 3}, "samples": 3, "confidence": 1}}, "class_distribution": {"No": 3, "Yes": 1}, "samples": 4, "confidence": 0.75}, "lte": {"type": "decision", "feature": {"name": "longitude", "type": "float"}, "split_value": -124.15875700000001, "children": {"gt": {"type": "decision", "feature": {"name": "monthly_charge", "type": "float"}, "split_value": 103.30000000000001, "children": {"gt": {"type": "decision", "feature": {"name": "monthly_charge", "type": "float"}, "split_value": 103.525, "children": {"gt": {"type": "decision", "feature": {"name": "avg_monthly_long_distance_charges", "type": "float"}, "split_value": 48.815, "children": {"gt": {"type": "decision", "feature": {"name": "total_revenue", "type": "float"}, "split_value": 9384.28, "children": {"gt": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "lte": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 1}, "samples": 1, "confidence": 1}}, "class_distribution": {"No": 1, "Yes": 1}, "samples": 2, "confidence": 0.5}, "lte": {"type": "decision", "feature": {"name": "total_charges", "type": "float"}, "split_value": 7865.5, "children": {"gt": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 56}, "samples": 56, "confidence": 1}, "lte": {"type": "decision", "feature": {"name": "avg_monthly_gb_download", "type": "float"}, "split_value": 2.5, "children": {"gt": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 83, "Yes": 13}, "samples": 96, "confidence": 0.8645833333333334}, "lte": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}}, "class_distribution": {"No": 83, "Yes": 14}, "samples": 97, "confidence": 0.8556701030927835}}, "class_distribution": {"No": 139, "Yes": 14}, "samples": 153, "confidence": 0.9084967320261438}}, "class_distribution": {"No": 140, "Yes": 15}, "samples": 155, "confidence": 0.9032258064516129}, "lte": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}}, "class_distribution": {"No": 140, "Yes": 16}, "samples": 156, "confidence": 0.8974358974358975}, "lte": {"type": "decision", "feature": {"name": "population", "type": "float"}, "split_value": 50641.5, "children": {"gt": {"type": "decision", "feature": {"name": "population", "type": "float"}, "split_value": 51280.5, "children": {"gt": {"type": "decision", "feature": {"name": "total_extra_data_charges", "type": "float"}, "split_value": 95, "children": {"gt": {"type": "decision", "feature": {"name": "device_protection_plan", "type": "string"}, "split_value": "device_protection_plan", "children": {"No": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "Yes": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 1}, "samples": 1, "confidence": 1}}, "class_distribution": {"No": 1, "Yes": 1}, "samples": 2, "confidence": 0.5}, "lte": {"type": "decision", "feature": {"name": "age", "type": "float"}, "split_value": 20, "children": {"gt": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 72, "Yes": 3}, "samples": 75, "confidence": 0.96}, "lte": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 1, "Yes": 1}, "samples": 2, "confidence": 0.5}}, "class_distribution": {"No": 73, "Yes": 4}, "samples": 77, "confidence": 0.948051948051948}}, "class_distribution": {"No": 74, "Yes": 5}, "samples": 79, "confidence": 0.9367088607594937}, "lte": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}}, "class_distribution": {"No": 74, "Yes": 6}, "samples": 80, "confidence": 0.925}, "lte": {"type": "decision", "feature": {"name": "age", "type": "float"}, "split_value": 79.5, "children": {"gt": {"type": "decision", "feature": {"name": "zip_code", "type": "float"}, "split_value": 91169, "children": {"gt": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 10}, "samples": 10, "confidence": 1}, "lte": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}}, "class_distribution": {"No": 10, "Yes": 1}, "samples": 11, "confidence": 0.9090909090909091}, "lte": {"type": "decision", "feature": {"name": "total_charges", "type": "float"}, "split_value": 6571.225, "children": {"gt": {"type": "decision", "feature": {"name": "total_charges", "type": "float"}, "split_value": 6584.325000000001, "children": {"gt": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 31, "Yes": 1}, "samples": 32, "confidence": 0.96875}, "lte": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}}, "class_distribution": {"No": 31, "Yes": 2}, "samples": 33, "confidence": 0.9393939393939394}, "lte": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 687, "Yes": 8}, "samples": 695, "confidence": 0.9884892086330935}}, "class_distribution": {"No": 718, "Yes": 10}, "samples": 728, "confidence": 0.9862637362637363}}, "class_distribution": {"No": 728, "Yes": 11}, "samples": 739, "confidence": 0.9851150202976996}}, "class_distribution": {"No": 802, "Yes": 17}, "samples": 819, "confidence": 0.9792429792429792}}, "class_distribution": {"No": 942, "Yes": 33}, "samples": 975, "confidence": 0.9661538461538461}, "lte": {"type": "decision", "feature": {"name": "longitude", "type": "float"}, "split_value": -124.1766055, "children": {"gt": {"type": "leaf", "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1}, "lte": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 4}, "samples": 4, "confidence": 1}}, "class_distribution": {"No": 4, "Yes": 1}, "samples": 5, "confidence": 0.8}}, "class_distribution": {"No": 946, "Yes": 34}, "samples": 980, "confidence": 0.9653061224489796}}, "class_distribution": {"No": 949, "Yes": 35}, "samples": 984, "confidence": 0.9644308943089431}}, "class_distribution": {"No": 1457, "Yes": 35}, "samples": 1492, "confidence": 0.9765415549597856}}, "class_distribution": {"No": 1463, "Yes": 36}, "samples": 1499, "confidence": 0.9759839893262174}}, "class_distribution": {"No": 1469, "Yes": 37}, "samples": 1506, "confidence": 0.9754316069057105}}, "class_distribution": {"No": 1472, "Yes": 38}, "samples": 1510, "confidence": 0.9748344370860927}}, "class_distribution": {"No": 1472, "Yes": 39}, "samples": 1511, "confidence": 0.9741892786234282}}, "class_distribution": {"No": 4139, "Yes": 1492}, "samples": 5631, "confidence": 0.7350381814952939}}, "class_distribution": {"No": 4139, "Yes": 1493}, "samples": 5632, "confidence": 0.7349076704545454}}, "class_distribution": {"No": 4139, "Yes": 1495}, "samples": 5634, "confidence": 0.7346467873624423}}