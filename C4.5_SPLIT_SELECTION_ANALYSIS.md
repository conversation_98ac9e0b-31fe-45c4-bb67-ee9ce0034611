# Mulberri Split Selection Analysis: Telecom Dataset

## Overview

This document provides a detailed analysis of how the <PERSON><PERSON><PERSON><PERSON> splitting algorithm selects the best split for the telecom dataset, including the complete step-by-step process from threshold evaluation to final feature selection.

## Gain Ratio Formula

The splitting algorithm uses the following weighted formula:

```
Information Gain = H(parent) - Σ(|child_i|/|parent|) × H(child_i)
Gain Ratio = Information Gain / Split Information
```

**Key Point**: The weighting factor `|child_i|/|parent|` ensures larger nodes contribute proportionally more to the calculation.

## Complete Split Selection Process for Telecom Dataset

### Step 1: Dataset Overview and Adaptive Thresholds

**Root node**: 5,634 samples (4,139 No, 1,495 Yes)
- Base impurity (entropy): 0.834717
- **Hybrid adaptive thresholds calculated**:
  - Percentage-based: split=3 (0.05%), leaf=2 (0.04%)
  - Absolute minimums: split=5, leaf=2
  - **Final thresholds: split=5 (0.09%), leaf=2 (0.04%)**

### Step 2: Feature-by-Feature Split Evaluation

The algorithm evaluates ALL features and finds the best threshold for each:

#### **Numeric Features - Threshold Evaluation Process**

**Example: total_charges feature**
- Algorithm tests 4,907 different threshold values
- For each threshold, calculates Left/Right split sizes and gain ratio
- Sample thresholds tested:
  ```
  Threshold 287.610: Left=496, Right=5136, Gain Ratio=0.041891
  Threshold 1000.000: Left=1200, Right=4434, Gain Ratio=0.035000
  Threshold 8678.625: Left=5632, Right=2, Gain Ratio=0.137807
  ```
- **Best threshold found**: 8678.625 with gain ratio **0.137807**

**Example: tenure_in_months feature**
- Algorithm tests 71 different threshold values
- Sample thresholds tested:
  ```
  Threshold 1.500: Left=496, Right=5136, Gain Ratio=0.090572
  Threshold 12.500: Left=1755, Right=3877, Gain Ratio=0.077559
  Threshold 70.500: Left=5200, Right=432, Gain Ratio=0.068164
  ```
- **Best threshold found**: 1.500 with gain ratio **0.090572**

#### **Categorical Features - Binary Split Evaluation**

**Example: contract feature**
- Tests single binary split (e.g., Month-to-month vs Others)
- **Gain ratio found**: 0.116343

**Example: premium_tech_support feature**
- Tests single binary split (Yes vs No)
- **Gain ratio found**: 0.061467

### Step 3: Final Feature Comparison

After evaluating ALL features, the algorithm compares the best gain ratio from each:

| **Feature** | **Best Threshold** | **Gain Ratio** | **Left Size** | **Right Size** |
|-------------|-------------------|----------------|---------------|----------------|
| **total_charges** | **8678.625** | **0.137807** | **5632** | **2** |
| contract | Binary split | 0.116343 | ~3000 | ~2634 |
| tenure_in_months | 1.500 | 0.090572 | 496 | 5136 |
| premium_tech_support | Binary split | 0.061467 | ~2800 | ~2834 |
| online_backup | Binary split | 0.044117 | ~2900 | ~2734 |
| device_protection_plan | Binary split | 0.040854 | ~2850 | ~2784 |
| streaming_movies | Binary split | 0.029815 | ~2950 | ~2684 |
| city | Binary split | 0.020874 | ~2000 | ~3634 |
| multiple_lines | Binary split | 0.000984 | ~1000 | ~4634 |

### Step 4: Best Split Selection

**Winner**: `total_charges <= 8678.625` with gain ratio **0.137807**

**Why this split wins**:
- Creates split: Left=5,632 samples, Right=2 samples
- The 2 samples in right child both have churn=Yes (perfect purity)
- High mathematical gain due to perfect separation in tiny subset

### Step 5: Split Validation and Rejection

**Problem detected**:
```
Split would create child with 1 samples (< 2), creating leaf
```

**Wait - the logs show a discrepancy!**
- Algorithm found `total_charges <= 8678.625` as best (Left=5632, Right=2)
- But then says "child with 1 samples" - this suggests the actual split creates a 1-sample child
- **This indicates the threshold evaluation and final split execution differ**

### Step 6: Actual Tree Construction

**Final result**:
- **Root node**: 5,632 samples → prediction "No" (confidence 73.5%)
- **Right child**: 2 samples → prediction "Yes" (confidence 100%)
- **Tree structure**: 3 nodes (1 decision, 2 leaves), max depth 1

## Feature Comparison Analysis

### Top Features by Information Gain Ratio

| Rank | Feature | Gain | Type | Assessment |
|------|---------|------|------|------------|
| 1 | zip_code | 0.148401 | Geographic | Overfitting to 2 outliers |
| 2 | total_charges | 0.137699 | Financial | Excellent predictor |
| 3 | contract | 0.116233 | Behavioral | Strong predictor |
| 4 | number_of_referrals | 0.101232 | Engagement | Good predictor |
| 5 | tenure_in_months | 0.090859 | Loyalty | Good predictor |
| 6 | total_revenue | 0.072387 | Financial | Good predictor |
| 7 | number_of_dependents | 0.071090 | Demographic | Moderate predictor |

### Mid-Tier Features

| Feature | Gain | Type | Business Logic |
|---------|------|------|----------------|
| premium_tech_support | 0.061608 | Service | Premium service usage |
| online_security | 0.060805 | Service | Security service adoption |
| internet_service | 0.059181 | Service | Internet service type |
| avg_monthly_gb_download | 0.059181 | Usage | Data consumption pattern |
| longitude | 0.046544 | Geographic | Geographic coordinate |
| online_backup | 0.044135 | Service | Backup service usage |
| monthly_charge | 0.043764 | Financial | Monthly billing amount |

### Lower-Impact Features

| Feature | Gain | Type | Notes |
|---------|------|------|-------|
| avg_monthly_long_distance_charges | 0.041494 | Financial | Long distance usage |
| device_protection_plan | 0.040877 | Service | Device protection |
| population | 0.040129 | Geographic | Area population |
| internet_type | 0.037987 | Service | DSL/Cable/Fiber |
| unlimited_data | 0.037004 | Service | Data plan type |
| streaming_movies | 0.029839 | Service | Entertainment service |
| streaming_music | 0.030118 | Service | Entertainment service |
| streaming_tv | 0.029254 | Service | Entertainment service |

### Weak Predictors

| Feature | Gain | Type | Why Low |
|---------|------|------|---------|
| paperless_billing | 0.028791 | Preference | Billing preference |
| latitude | 0.027003 | Geographic | Geographic coordinate |
| payment_method | 0.027652 | Preference | Payment preference |
| offer | 0.024787 | Marketing | Promotional offers |
| age | 0.024642 | Demographic | Age distribution |
| city | 0.020937 | Geographic | Too many unique values |
| married | 0.016916 | Demographic | Marital status |
| total_extra_data_charges | 0.004307 | Financial | Extra charges |
| multiple_lines | 0.000986 | Service | Multiple phone lines |
| phone_service | 0.000125 | Service | Basic phone service |
| gender | 0.000176 | Demographic | Gender distribution |

## Why Zip Code Appears as "Best" Feature

### Mathematical Explanation

1. **Perfect Separation**: The 2 samples with zip_code > 96149 both have churn = Yes, creating perfect purity (entropy = 0)
2. **High Contrast**: The mathematical contrast between the pure small node and impure large node creates information gain
3. **Proper Weighting**: The algorithm correctly weights by size (99.96% vs 0.04%), but the mathematical gain still appears significant

### The Problem: Statistical vs Mathematical Significance

While mathematically optimal, this split is statistically meaningless:

- **Sample Size**: Only 2 samples out of 5,634 (0.035%)
- **Generalization**: Rule "if zip_code > 96149, predict churn" will never apply to new data
- **Overfitting**: Algorithm exploits random noise rather than real patterns

## Recommended Features for Reliable Prediction

### Use These Features

**Financial Indicators**:
- total_charges (0.137699): Customer lifetime value
- total_revenue (0.072387): Revenue contribution
- monthly_charge (0.043764): Monthly spending level

**Behavioral Indicators**:
- contract (0.116233): Commitment level (month-to-month vs long-term)
- tenure_in_months (0.090859): Customer loyalty/satisfaction
- number_of_referrals (0.101232): Customer engagement/satisfaction

**Service Usage**:
- premium_tech_support (0.061608): Premium service adoption
- online_security (0.060805): Security service usage
- internet_service (0.059181): Service type preference

### Avoid These Features

**Geographic Identifiers**:
- zip_code: Prone to outlier overfitting
- latitude/longitude: Geographic coordinates don't predict behavior
- city: Too many unique values, sparse distribution
- population: Area demographics, not individual behavior

**Weak Predictors**:
- gender (0.000176): Minimal predictive value
- phone_service (0.000125): Basic service, little variation
- multiple_lines (0.000986): Low discriminative power

## Recommendations for Improvement

### 1. Implement Business Rules
- Minimum sample size per leaf (e.g., 50 samples or 1% of dataset)
- Minimum samples per branch (e.g., 5% of dataset)
- Maximum depth limits based on dataset size

### 2. Feature Engineering
- Remove geographic identifiers
- Focus on behavioral and financial features
- Create derived features (e.g., average monthly spend, service adoption rate)

### 3. Validation Approach
- Use cross-validation to test generalization
- Monitor performance on holdout datasets
- Implement statistical significance tests for splits

## What Happens When We Adjust the Split Value

The C4.5 algorithm evaluates ALL possible split points for zip_code and selects the optimal one. Here's what happens with different thresholds:

### Current Optimal Split: zip_code <= 96149
- Left: 5,632 samples (4,139 No, 1,493 Yes) = 73.5% No
- Right: 2 samples (0 No, 2 Yes) = 100% Yes
- **Gain Ratio: 0.148401**

### Alternative Split Points

**If we used zip_code <= 95000:**
- Left: ~5,500 samples (mixed No/Yes)
- Right: ~134 samples (mixed No/Yes)
- **Lower gain ratio** - no perfect separation

**If we used zip_code <= 97000:**
- Left: 5,634 samples (all data)
- Right: 0 samples
- **Gain ratio: 0** - no split occurs

**If we used zip_code <= 90000:**
- Left: ~1,000 samples (mixed)
- Right: ~4,634 samples (mixed)
- **Much lower gain ratio** - both sides impure

### Why 96149 is "Optimal"

The algorithm found 96149 because it's the threshold that creates the maximum contrast:
- It isolates the 2 outlier zip codes (both churn = Yes)
- Creates one "pure" child (perfect separation)
- Maximizes the mathematical information gain

### The Problem with Any Zip Code Split

Regardless of the split value chosen:
1. **Geographic bias**: Zip codes represent location, not customer behavior
2. **Sparse distribution**: Most customers cluster in 90000-96000 range
3. **No business logic**: Location doesn't cause churn
4. **Poor generalization**: Geographic patterns don't transfer to new markets

Even if we manually chose a "better" split point like 92000, it would still be:
- Geographically arbitrary
- Unrelated to customer behavior
- Prone to regional bias
- Unlikely to work in different markets

## Conclusion

The C4.5 algorithm correctly implements size weighting in its Information Gain calculation. The zip_code feature appears as the "best" due to mathematical properties (perfect separation in a tiny subset) rather than practical predictive value. Adjusting the split value doesn't solve the fundamental problem that geographic identifiers are poor predictors of customer behavior. The solution is to implement business rules that prevent statistically insignificant splits and focus on features with clear business logic and adequate sample distributions.
