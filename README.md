# Mulberri
Efficient, production-grade C4.5 decision tree implementation written in Go.

## Highlights
- **High Performance**: Optimized for speed and memory efficiency
- **Production Ready**: Designed for offline training and fast real-time inference
- **Interpretable**: Provides human-readable decision paths and rules
- **Well Tested**: Comprehensive unit test coverage and benchmarks
- **Clean Architecture**: Modular design with clear separation of concerns
- **CLI Interface**: Easy-to-use command-line tools for training and prediction

## Usage

### Training a Model
```bash
go run cmd/mulberri/main.go train \
  --features examples/loan_approval_features.yaml \
  --input examples/loan_approval.csv \
  --target approved \
  --output examples/loan_approval_model.dt \
  --verbose
```

### Making Predictions

#### Simple Predictions
```bash
go run cmd/mulberri/main.go predict \
  --input examples/loan_approval_predict.csv \
  --model examples/loan_approval_model.dt \
  --output examples/predictions_simple.csv
```

#### Detailed Predictions
```bash
go run cmd/mulberri/main.go predict \
  --input examples/loan_approval_predict.csv \
  --model examples/loan_approval_model.dt \
  --output examples/predictions_detailed.csv \
  --detailed
```

## Prediction Output Columns

When using the `--detailed` flag, the prediction output includes the following columns:

| Column | Description | Example |
|--------|-------------|---------|
| **row_index** | Zero-based index of the input row | `0`, `1`, `2` |
| **prediction** | Final prediction value from the decision tree | `yes`, `no`, `approved` |
| **confidence** | Prediction confidence (0.0-1.0) based on leaf node purity | `1.0000`, `0.8500` |
| **decision_rule** | Human-readable IF-THEN rule describing the decision path | `IF income <= 54000 AND education != college THEN no` |
| **decision_path** | Step-by-step path through the tree with node details | `income <= 54000 → education != college → LEAF[no] (confidence: 1.000)` |
| **input_features** | Original input features as JSON | `{"income":32000, "education":"high_school", "age":27}` |
| **class_distribution** | Training sample distribution at the reached leaf node | `{no:9}`, `{yes:8, no:2}` |
| **error** | Error message if prediction failed, empty if successful | `""` or `"feature 'age' not found"` |

### Understanding Confidence
- **1.0000**: Perfect confidence - all training samples at this leaf had the same target value
- **0.8500**: 85% confidence - e.g., 17 out of 20 training samples had the predicted class
- **0.5000**: Low confidence - indicates uncertainty, often from balanced leaf nodes

### Understanding Decision Paths
The decision path shows exactly how the tree made its decision:
- `income <= 54000`: Numerical comparison (went left because income was ≤ threshold)
- `education != college`: Categorical comparison (went right because education was not "college")
- `LEAF[no]`: Reached a leaf node with prediction "no"

## Project Structure
```
mulberri/
├── cmd/mulberri/           # CLI entry point and main application
├── internal/
│   ├── data/               # Data handling and feature processing
│   │   ├── dataset/        # Dataset structures and operations
│   │   └── features/       # Feature type definitions and conversion
│   ├── io/                 # Input/output operations
│   │   ├── cli/            # Command-line interface implementation
│   │   ├── formats/        # File format handlers (CSV, YAML)
│   │   └── persistence/    # Model serialization and loading
│   ├── prediction/         # Prediction service and batch processing
│   ├── training/           # Tree building and split evaluation
│   ├── tree/               # Decision tree data structures
│   ├── types/              # Shared type definitions
│   └── utils/              # Utility packages (logging, profiling, etc.)
├── examples/               # Example datasets and configurations
├── scripts/                # Build and maintenance scripts
└── docs/                   # Documentation
```

## Development

### Building
```bash
go build -o bin/mulberri cmd/mulberri/main.go
```

### Testing
```bash
# Run all tests
go test ./...

# Run tests with coverage
go test -cover ./...

# Run specific package tests
go test ./internal/training
```

### Code Quality

#### Checking for Unused Code
To maintain code quality, periodically check for unused functions:

```bash
# Install deadcode tool
go install golang.org/x/tools/cmd/deadcode@latest

# Check for unused functions
deadcode ./...

# If deadcode is not in PATH, use full path
~/go/bin/deadcode ./...
```

**Note**: The `deadcode` tool requires Go 1.24+. If you're using Go 1.23 or earlier, you may need to update Go or use alternative tools.

#### Other Quality Checks
```bash
# Vet code for common issues
go vet ./...

# Format code
go fmt ./...

# Run linter (if available)
golangci-lint run
```

## Architecture

### Core Components
- **CLI Layer** (`internal/io/cli`): Command-line interface and configuration
- **Data Layer** (`internal/data`): Dataset handling and feature processing
- **Training Engine** (`internal/training`): C4.5 algorithm implementation
- **Prediction Service** (`internal/prediction`): Batch prediction with concurrency
- **Tree Structures** (`internal/tree`): Decision tree representation
- **I/O Handlers** (`internal/io`): File format support and model persistence

### Key Features
- **C4.5 Algorithm**: Information gain ratio for split selection
- **Concurrent Processing**: Worker pools for batch predictions
- **Memory Efficiency**: Object pooling and optimized data structures
- **Comprehensive Logging**: Operation-specific log files with rotation
- **Profiling Support**: Built-in CPU and memory profiling
- **Type Safety**: Strong typing with generics for dataset operations

## Contributing

### Branch Access
- Feature branches → `Creators`  
- Main branch → Merge requires `Founders` approval

### Code Standards
- Run `deadcode ./...` before submitting PRs to ensure no unused code
- Maintain test coverage above 70% for core packages
- Follow Go naming conventions and add comprehensive documentation
- Use the existing logging and error handling patterns