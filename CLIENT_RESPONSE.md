Dear Client,

Yes, the algorithm DOES weight by node size. We use Gain Ratio (not just Information Gain):

```
Information Gain = H(parent) - Σ(|child_i|/|parent|) × H(child_i)
Gain Ratio = Information Gain / Split Information
```

The algorithm evaluates ALL features systematically:
1. **For each feature**: Tests all possible thresholds (e.g., total_charges tested 4,907 thresholds)
2. **Finds best threshold per feature**: total_charges <= 8678.625 (gain: 0.137807)
3. **Compares across features**: total_charges beats contract (0.116343), tenure_in_months (0.090572)
4. **Selects winner**: total_charges <= 8678.625 creates Left=5,632, Right=2 samples

The total_charges split wins despite proper weighting because the 2-sample branch has perfect purity (100% Yes), creating mathematical contrast even though it's statistically meaningless.

However, this feature should NOT be used because:
- Only 2 out of 5,634 samples (0.035%) in the minority split
- Creates a rule that will never apply to new customers
- Classic overfitting to outliers, not real patterns

Better approach: Use features with balanced splits like contract, tenure_in_months.

See C4.5_SPLIT_SELECTION_ANALYSIS.md for complete threshold evaluation process.
