{"features": [{"name": "age", "type": "integer"}, {"name": "avg_monthly_gb_download", "type": "float"}, {"name": "avg_monthly_long_distance_charges", "type": "float"}, {"name": "city", "type": "string"}, {"name": "contract", "type": "string"}, {"name": "device_protection_plan", "type": "string"}, {"name": "gender", "type": "string"}, {"name": "internet_service", "type": "string"}, {"name": "internet_type", "type": "string"}, {"name": "latitude", "type": "float"}, {"name": "longitude", "type": "float"}, {"name": "married", "type": "string"}, {"name": "monthly_charge", "type": "float"}, {"name": "multiple_lines", "type": "string"}, {"name": "number_of_dependents", "type": "integer"}, {"name": "number_of_referrals", "type": "integer"}, {"name": "offer", "type": "string"}, {"name": "online_backup", "type": "string"}, {"name": "online_security", "type": "string"}, {"name": "paperless_billing", "type": "string"}, {"name": "payment_method", "type": "string"}, {"name": "phone_service", "type": "string"}, {"name": "population", "type": "integer"}, {"name": "premium_tech_support", "type": "string"}, {"name": "streaming_movies", "type": "string"}, {"name": "streaming_music", "type": "string"}, {"name": "streaming_tv", "type": "string"}, {"name": "tenure_in_months", "type": "integer"}, {"name": "total_charges", "type": "float"}, {"name": "total_extra_data_charges", "type": "integer"}, {"name": "total_long_distance_charges", "type": "float"}, {"name": "total_refunds", "type": "float"}, {"name": "total_revenue", "type": "float"}, {"name": "unlimited_data", "type": "string"}, {"name": "zip_code", "type": "integer"}], "classes": ["No", "Yes"], "metadata": {"created_at": "2025-10-01T14:58:54.182287717+03:00", "algorithm": "C4.5", "max_depth": 3, "min_samples": 29, "criterion": "entropy", "total_nodes": 1, "leaf_nodes": 1, "training_samples": 5634, "target_column": "churn"}, "root": {"type": "leaf", "prediction": "No", "class_distribution": {"No": 4139, "Yes": 1495}, "samples": 5634, "confidence": 0.7346467873624423}}