# Feature configuration for telecom churn prediction dataset
# Target column: churn

gender:
  type: nominal
  handle_as: string

age:
  type: numeric
  handle_as: integer

married:
  type: binary
  handle_as: string

number_of_dependents:
  type: numeric
  handle_as: integer

city:
  type: nominal
  handle_as: string

zip_code:
  type: numeric
  handle_as: integer

population:
  type: numeric
  handle_as: integer

latitude:
  type: numeric
  handle_as: float

longitude:
  type: numeric
  handle_as: float

number_of_referrals:
  type: numeric
  handle_as: integer

tenure_in_months:
  type: numeric
  handle_as: integer

offer:
  type: nominal
  handle_as: string

phone_service:
  type: binary
  handle_as: string

avg_monthly_long_distance_charges:
  type: numeric
  handle_as: float

multiple_lines:
  type: binary
  handle_as: string

internet_service:
  type: binary
  handle_as: string

internet_type:
  type: nominal
  handle_as: string

avg_monthly_gb_download:
  type: numeric
  handle_as: float

online_security:
  type: binary
  handle_as: string

online_backup:
  type: binary
  handle_as: string

device_protection_plan:
  type: binary
  handle_as: string

premium_tech_support:
  type: binary
  handle_as: string

streaming_tv:
  type: binary
  handle_as: string

streaming_movies:
  type: binary
  handle_as: string

streaming_music:
  type: binary
  handle_as: string

unlimited_data:
  type: binary
  handle_as: string

contract:
  type: nominal
  handle_as: string

paperless_billing:
  type: binary
  handle_as: string

payment_method:
  type: nominal
  handle_as: string

monthly_charge:
  type: numeric
  handle_as: float

total_charges:
  type: numeric
  handle_as: float

total_refunds:
  type: numeric
  handle_as: float

total_extra_data_charges:
  type: numeric
  handle_as: integer

total_long_distance_charges:
  type: numeric
  handle_as: float

total_revenue:
  type: numeric
  handle_as: float

churn:
  type: binary
  handle_as: string