{"features": [{"name": "petal_length", "type": "float"}, {"name": "petal_width", "type": "float"}, {"name": "sepal_length", "type": "float"}, {"name": "sepal_width", "type": "float"}], "classes": ["setosa", "versicolor", "virginica"], "metadata": {"created_at": "2025-10-02T15:09:37.820345221+03:00", "algorithm": "C4.5", "max_depth": 2, "min_samples": 2, "criterion": "entropy", "total_nodes": 5, "leaf_nodes": 3, "training_samples": 150, "target_column": "species"}, "root": {"type": "decision", "feature": {"name": "petal_length", "type": "float"}, "split_value": 2.45, "children": {"gt": {"type": "decision", "feature": {"name": "petal_width", "type": "float"}, "split_value": 1.75, "children": {"gt": {"type": "leaf", "prediction": "virginica", "class_distribution": {"versicolor": 1, "virginica": 45}, "samples": 46, "confidence": 0.9782608695652174}, "lte": {"type": "leaf", "prediction": "versicolor", "class_distribution": {"versicolor": 49, "virginica": 5}, "samples": 54, "confidence": 0.9074074074074074}}, "class_distribution": {"versicolor": 50, "virginica": 50}, "samples": 100, "confidence": 0.5}, "lte": {"type": "leaf", "prediction": "setosa", "class_distribution": {"setosa": 50}, "samples": 50, "confidence": 1}}, "class_distribution": {"setosa": 50, "versicolor": 50, "virginica": 50}, "samples": 150, "confidence": 0.3333333333333333}}