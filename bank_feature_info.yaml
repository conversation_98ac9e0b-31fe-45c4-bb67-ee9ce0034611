age:
  type: numeric
  handle_as: float
job:
  type: nominal
  handle_as: string
marital:
  type: nominal
  handle_as: string
education:
  type: nominal
  handle_as: string
default:
  type: nominal
  handle_as: string
balance:
  type: numeric
  handle_as: float
housing:
  type: nominal
  handle_as: string
loan:
  type: nominal
  handle_as: string
contact:
  type: nominal
  handle_as: string
day:
  type: numeric
  handle_as: float
month:
  type: nominal
  handle_as: string
duration:
  type: numeric
  handle_as: float
campaign:
  type: numeric
  handle_as: float
pdays:
  type: numeric
  handle_as: float
previous:
  type: numeric
  handle_as: float
poutcome:
  type: nominal
  handle_as: string
y:
  type: nominal
  handle_as: string
