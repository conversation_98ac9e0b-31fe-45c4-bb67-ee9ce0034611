# Decision Tree Parameter Testing and Analysis

## Overview

This document presents a comprehensive analysis of the <PERSON><PERSON>ber<PERSON> decision tree implementation across different datasets and parameter configurations. The testing was conducted to evaluate parameter sensitivity, tree optimization, and scaling behavior.

## Test Summary

| Dataset     | Size | Features | Target  | Default   | Aggressive | Conservative | Extreme     |
| ----------- | ---- | -------- | ------- | --------- | ---------- | ------------ | ----------- |
| **Iris**    | 150  | 4        | species | ✅ 9 nodes | ✅ 11 nodes | ✅ 5 nodes    | -           |
| **Tennis**  | 14   | 4        | Play    | ✅ 8 nodes | ✅ 8 nodes  | ✅ 1 node     | -           |
| **Bank**    | 3616 | 16       | y       | ❌ 1 node  | ❌ 1 node   | -            | ✅ 94 nodes  |
| **Telecom** | 5634 | 35       | churn   | ❌ 1 node  | ❌ 1 node   | -            | ✅ 450 nodes |

## Parameter Configurations

### Default Parameters
- `split_pct=0.01` (1%)
- `leaf_pct=0.005` (0.5%)
- `min_improvement=0.01` (1%)
- `alpha=1.5`

### Aggressive Parameters
- `split_pct=0.002-0.005`
- `leaf_pct=0.001-0.002`
- `min_improvement=0.005`
- `alpha=2.0-3.0`

### Conservative Parameters
- `split_pct=0.02-0.3`
- `leaf_pct=0.01-0.2`
- `min_improvement=0.02-0.5`
- `alpha=0.5-1.0`

### Extreme Parameters
- `split_pct=0.0001`
- `leaf_pct=0.0001`
- `min_improvement=0.001`
- `alpha=3.0`

## Detailed Results

### 1. Iris Dataset (150 samples, 4 features)

**Default Configuration:**
- Tree: 9 nodes (5 leaves), depth 3
- Effective max depth: 3 (α=1.5 × log₂(4) = 3)
- Adaptive thresholds: split=2 (1.33%), leaf=1 (0.67%)
- First split: `petal_width <= 0.8` (gain: 1.000000)
- Perfect separation of setosa class

**Aggressive Configuration:**
- Tree: 11 nodes (6 leaves), depth 4
- Effective max depth: 4 (α=2.0 × log₂(4) = 4)
- Adaptive thresholds: split=3 (2.00%), leaf=2 (1.33%)
- More detailed decision boundaries

**Conservative Configuration:**
- Tree: 5 nodes (3 leaves), depth 2
- Effective max depth: 2 (α=1.0 × log₂(4) = 2)
- Adaptive thresholds: split=8 (5.33%), leaf=3 (2.00%)
- Simpler, more generalized model

### 2. Tennis Dataset (14 samples, 4 features)

**Default Configuration:**
- Tree: 8 nodes (5 leaves), depth 2
- Effective max depth: 3 (α=1.5 × log₂(4) = 3)
- Adaptive thresholds: split=2 (14.29%), leaf=1 (7.14%)
- First split: `Outlook` categorical (gain: 0.156428)
- All leaf nodes achieved 100% confidence

**Aggressive Configuration:**
- Tree: 8 nodes (5 leaves), depth 2
- Effective max depth: 6 (α=3.0 × log₂(4) = 6)
- Adaptive thresholds: split=2 (14.29%), leaf=1 (7.14%)
- Same structure as default (dataset too small for differences)

**Conservative Configuration:**
- Tree: 1 node (1 leaf), depth 0
- Effective max depth: 1 (α=0.5 × log₂(4) = 1)
- Adaptive thresholds: split=5 (35.71%), leaf=3 (21.43%)
- Best split gain (0.156428) below threshold (0.5)

### 3. Bank Dataset (3616 samples, 16 features)

**Default & Aggressive Configurations:**
- Tree: 1 node (1 leaf), depth 0
- Best split: `age <= 86.500` (gain: 0.235081)
- Problem: Split would create child with only 1 sample
- Minimum leaf size constraints prevented tree growth

**Extreme Configuration:**
- Tree: 94 nodes (48 leaves), depth 12
- Effective max depth: 12 (α=3.0 × log₂(16) = 12)
- Adaptive thresholds: split=2 (0.06%), leaf=1 (0.03%)
- Successfully built complex tree structure

### 4. Telecom Dataset (5634 samples, 35 features)

**Default & Aggressive Configurations:**
- Tree: 1 node (1 leaf), depth 0
- Best split: `zip_code <= 96149.000` (gain: 0.148401)
- Problem: Split would create child with only 2 samples
- Minimum leaf size constraints prevented tree growth

**Extreme Configuration:**
- Tree: 450 nodes (354 leaves), depth 15
- Effective max depth: 15 (α=3.0 × log₂(35) = 15)
- Adaptive thresholds: split=2 (0.04%), leaf=1 (0.02%)
- Successfully built very complex tree structure

## Key Observations

### 1. Dataset Size Impact
- **Small datasets** (≤150 samples): Work well with default parameters
- **Medium datasets** (150-1000 samples): May need slightly aggressive parameters
- **Large datasets** (>1000 samples): Often require extreme parameters for meaningful trees

### 2. Parameter Sensitivity
- **Split/Leaf Percentages**: Most critical for large datasets
- **Min Split Improvement**: Affects tree complexity and overfitting
- **Max Depth Alpha**: Controls tree depth scaling with feature count

### 3. Adaptive Threshold Behavior
- Formula: `max(ceil(dataset_size × percent), absolute_min)`
- Absolute minimums: split=2, leaf=1
- Prevents overly restrictive constraints on small datasets

### 4. Feature Count Impact
- More features → higher effective max depth
- Formula: `effective_depth = alpha × log₂(features)`
- Allows deeper trees for high-dimensional data

## Recommendations

### Parameter Selection Guidelines

**Small Datasets (< 100 samples):**
- Use default or conservative parameters
- Risk of overfitting with aggressive settings

**Medium Datasets (100-1000 samples):**
- Start with default parameters
- Use aggressive if tree is too simple

**Large Datasets (> 1000 samples):**
- Often need aggressive or extreme parameters
- Monitor for overfitting with validation data

### Best Practices

1. **Start Conservative**: Begin with default parameters
2. **Gradual Adjustment**: Incrementally make parameters more aggressive
3. **Monitor Complexity**: Watch tree size and depth
4. **Validate Performance**: Use holdout data to check generalization
5. **Consider Domain**: Some domains benefit from simpler models

## Technical Implementation Notes

### Effective Max Depth Calculation
```
if explicit_depth > 0:
    effective_depth = explicit_depth
else:
    effective_depth = ceil(alpha × log₂(feature_count))
```

### Adaptive Threshold Calculation
```
split_threshold = max(ceil(dataset_size × split_pct), 2)
leaf_threshold = max(ceil(dataset_size × leaf_pct), 1)
```

### Stopping Criteria Priority
1. Maximum depth reached
2. Insufficient samples for splitting
3. Node is pure (entropy < 0.1)
4. Insufficient samples for meaningful leaves
5. Split improvement below threshold

## Conclusion

The Mulberri decision tree implementation demonstrates excellent parameter sensitivity and scaling behavior. The grid-search optimized parameters provide fine-grained control over tree complexity, with adaptive thresholds ensuring reasonable behavior across different dataset sizes. Large datasets require more aggressive parameters to overcome minimum sample constraints, while small datasets work well with default settings.
